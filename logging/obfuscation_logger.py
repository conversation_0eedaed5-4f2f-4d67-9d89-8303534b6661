"""
混淆日志记录器

专门用于记录混淆过程的详细信息，包括映射关系、错误追踪、处理统计等。
基于ARCHITECTURE.md中的日志系统设计实现。
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .logger import Logger


class ObfuscationLogger:
    """
    混淆日志记录器类
    
    功能:
    - 记录混淆映射关系 (原名称 -> 新名称)
    - 记录被忽略的关键词及原因
    - 记录错误和警告信息
    - 生成各种统计报告
    - 导出结构化的日志文件
    """
    
    def __init__(self, output_dir: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化混淆日志记录器
        
        Args:
            output_dir: 日志输出目录
            config: 日志配置，包含各种日志文件名配置
        """
        self.output_dir = output_dir
        self.config = config or {}
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化各种日志数据结构
        self.mapping_log: Dict[str, Dict[str, str]] = {
            'folders': {},      # 文件夹映射
            'files': {},        # 文件映射
            'classes': {},      # 类名映射
            'properties': {},   # 属性映射
            'methods': {},      # 方法映射
            'variables': {},    # 变量映射
            'constants': {},    # 常量映射
            'enums': {},        # 枚举映射
            'delegates': {},    # 代理映射
            'blocks': {},       # Block映射
            'c_functions': {},  # C函数映射
            'strings': {},      # 字符串映射
            'images': {}        # 图片映射
        }
        
        self.ignored_keywords: List[Dict[str, Any]] = []  # 被忽略的关键词
        self.error_log: List[Dict[str, Any]] = []         # 错误日志
        self.process_log: List[Dict[str, Any]] = []       # 处理过程日志
        self.statistics: Dict[str, Any] = {}              # 统计信息
        
        # 项目关键词列表
        self.framework_keywords: List[str] = []           # 框架关键词
        self.string_keywords: List[str] = []              # 字符串关键词
        self.constant_strings: List[str] = []             # 常量字符串
        self.all_strings: List[str] = []                  # 所有字符串
        
        # 创建基础日志器
        process_log_file = os.path.join(output_dir, self._get_log_filename('process_log'))
        self.logger = Logger("ObfuscationLogger", log_file=process_log_file)
        
        # 记录开始时间
        self.start_time = datetime.now()
        self.logger.info(f"混淆日志记录器初始化完成 - 输出目录: {output_dir}")
    
    def _get_log_filename(self, log_type: str) -> str:
        """
        获取指定类型的日志文件名
        
        Args:
            log_type: 日志类型
            
        Returns:
            日志文件名
        """
        default_filenames = {
            'ignored_keywords': '被忽略的关键词日志.txt',
            'constant_strings': '工程中常量字符串列表.txt',
            'all_strings': '工程中所有字符串列表.txt',
            'framework_keywords': 'project_framework_keywords.txt',
            'string_keywords': 'project_all_string_words.txt',
            'obfuscation_mapping': 'obfuscation_mapping.json',
            'error_trace': 'error_trace.log',
            'process_log': 'process.log',
            'obfuscation_log': 'obfuscation.log'
        }
        
        # 从配置中获取文件名，如果没有则使用默认值
        log_files_config = self.config.get('log_files', {})
        return log_files_config.get(log_type, default_filenames.get(log_type, f'{log_type}.log'))
    
    def log_mapping(self, category: str, original: str, obfuscated: str, 
                   file_path: Optional[str] = None, line_number: Optional[int] = None):
        """
        记录混淆映射关系
        
        Args:
            category: 类别 (如: properties, methods, classes等)
            original: 原始名称
            obfuscated: 混淆后名称
            file_path: 文件路径 (可选)
            line_number: 行号 (可选)
        """
        if category in self.mapping_log:
            self.mapping_log[category][original] = obfuscated
            
            # 记录到统一混淆日志
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'category': category,
                'original': original,
                'obfuscated': obfuscated,
                'file_path': file_path,
                'line_number': line_number
            }
            
            # 写入统一混淆日志文件
            self._append_to_obfuscation_log(log_entry)
            
            self.logger.debug(f"记录映射 [{category}]: {original} -> {obfuscated}")
    
    def log_ignored_keyword(self, keyword: str, reason: str, category: str, 
                          file_path: Optional[str] = None):
        """
        记录被忽略的关键词
        
        Args:
            keyword: 被忽略的关键词
            reason: 忽略原因
            category: 类别
            file_path: 文件路径 (可选)
        """
        ignored_entry = {
            'timestamp': datetime.now().isoformat(),
            'keyword': keyword,
            'reason': reason,
            'category': category,
            'file_path': file_path
        }
        
        self.ignored_keywords.append(ignored_entry)
        self.logger.debug(f"忽略关键词 [{category}]: {keyword} - {reason}")
    
    def log_error(self, message: str, error_code: Optional[str] = None, 
                 file_path: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        """
        记录错误信息
        
        Args:
            message: 错误消息
            error_code: 错误代码 (可选)
            file_path: 文件路径 (可选)
            context: 错误上下文 (可选)
        """
        error_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': 'ERROR',
            'message': message,
            'error_code': error_code,
            'file_path': file_path,
            'context': context
        }
        
        self.error_log.append(error_entry)
        self.logger.error(f"错误: {message}")
    
    def log_warning(self, message: str, file_path: Optional[str] = None, 
                   context: Optional[Dict[str, Any]] = None):
        """
        记录警告信息
        
        Args:
            message: 警告消息
            file_path: 文件路径 (可选)
            context: 警告上下文 (可选)
        """
        warning_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': 'WARNING',
            'message': message,
            'file_path': file_path,
            'context': context
        }
        
        self.error_log.append(warning_entry)
        self.logger.warning(f"警告: {message}")
    
    def log_process_step(self, step_name: str, status: str, 
                        result_count: Optional[int] = None, 
                        duration: Optional[float] = None,
                        details: Optional[Dict[str, Any]] = None):
        """
        记录处理步骤
        
        Args:
            step_name: 步骤名称
            status: 状态 (START, COMPLETE, ERROR)
            result_count: 处理结果数量 (可选)
            duration: 耗时 (可选)
            details: 详细信息 (可选)
        """
        process_entry = {
            'timestamp': datetime.now().isoformat(),
            'step_name': step_name,
            'status': status,
            'result_count': result_count,
            'duration': duration,
            'details': details
        }
        
        self.process_log.append(process_entry)
        
        if status == 'START':
            self.logger.info(f"开始步骤: {step_name}")
        elif status == 'COMPLETE':
            if result_count is not None and duration is not None:
                self.logger.info(f"完成步骤: {step_name} - 处理了 {result_count} 项 (耗时: {duration:.2f}s)")
            else:
                self.logger.info(f"完成步骤: {step_name}")
        elif status == 'ERROR':
            self.logger.error(f"步骤失败: {step_name}")
    
    def set_framework_keywords(self, keywords: List[str]):
        """设置框架关键词列表"""
        self.framework_keywords = keywords
        self.logger.info(f"设置框架关键词: {len(keywords)} 个")
    
    def set_string_keywords(self, keywords: List[str]):
        """设置字符串关键词列表"""
        self.string_keywords = keywords
        self.logger.info(f"设置字符串关键词: {len(keywords)} 个")
    
    def set_constant_strings(self, strings: List[str]):
        """设置常量字符串列表"""
        self.constant_strings = strings
        self.logger.info(f"设置常量字符串: {len(strings)} 个")
    
    def set_all_strings(self, strings: List[str]):
        """设置所有字符串列表"""
        self.all_strings = strings
        self.logger.info(f"设置所有字符串: {len(strings)} 个")
    
    def _append_to_obfuscation_log(self, log_entry: Dict[str, Any]):
        """
        追加记录到统一混淆日志文件
        
        Args:
            log_entry: 日志条目
        """
        obfuscation_log_file = os.path.join(self.output_dir, self._get_log_filename('obfuscation_log'))
        
        try:
            with open(obfuscation_log_file, 'a', encoding='utf-8') as f:
                # 写入格式化的日志条目
                timestamp = log_entry['timestamp']
                category = log_entry['category']
                original = log_entry['original']
                obfuscated = log_entry['obfuscated']
                
                log_line = f"[{timestamp}] {category}: {original} -> {obfuscated}"
                
                if log_entry.get('file_path'):
                    log_line += f" (文件: {log_entry['file_path']}"
                    if log_entry.get('line_number'):
                        log_line += f", 行: {log_entry['line_number']}"
                    log_line += ")"
                
                f.write(log_line + '\n')
                
        except Exception as e:
            self.logger.error(f"写入统一混淆日志失败: {e}")
    
    def calculate_statistics(self) -> Dict[str, Any]:
        """
        计算混淆统计信息
        
        Returns:
            统计信息字典
        """
        total_mappings = sum(len(mappings) for mappings in self.mapping_log.values())
        
        self.statistics = {
            '总体统计': {
                '混淆项目总数': total_mappings,
                '被忽略关键词数': len(self.ignored_keywords),
                '错误数量': len([e for e in self.error_log if e['level'] == 'ERROR']),
                '警告数量': len([e for e in self.error_log if e['level'] == 'WARNING']),
                '处理步骤数': len(self.process_log),
                '开始时间': self.start_time.isoformat(),
                '当前时间': datetime.now().isoformat()
            },
            '各类型统计': {}
        }
        
        # 计算各类型的统计信息
        for category, mappings in self.mapping_log.items():
            self.statistics['各类型统计'][category] = {
                '混淆数量': len(mappings),
                '混淆率': f"{len(mappings)}" if len(mappings) > 0 else "0"
            }
        
        return self.statistics

    def export_logs(self):
        """
        导出所有日志文件

        生成各种格式的日志文件，包括:
        - 混淆映射JSON文件
        - 被忽略关键词文本文件
        - 错误追踪日志文件
        - 各种关键词列表文件
        """
        try:
            # 1. 导出混淆映射JSON文件
            self._export_mapping_json()

            # 2. 导出被忽略关键词文件
            self._export_ignored_keywords()

            # 3. 导出错误追踪日志
            self._export_error_trace()

            # 4. 导出关键词列表文件
            self._export_keyword_lists()

            # 5. 生成统计报告
            self._export_statistics_report()

            self.logger.info("所有日志文件导出完成")

        except Exception as e:
            self.logger.error(f"导出日志文件失败: {e}")

    def _export_mapping_json(self):
        """导出混淆映射JSON文件"""
        mapping_file = os.path.join(self.output_dir, self._get_log_filename('obfuscation_mapping'))

        # 添加元数据
        export_data = {
            'metadata': {
                'export_time': datetime.now().isoformat(),
                'total_mappings': sum(len(mappings) for mappings in self.mapping_log.values()),
                'categories': list(self.mapping_log.keys())
            },
            'mappings': self.mapping_log,
            'statistics': self.calculate_statistics()
        }

        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        self.logger.info(f"导出混淆映射文件: {mapping_file}")

    def _export_ignored_keywords(self):
        """导出被忽略关键词文件"""
        ignored_file = os.path.join(self.output_dir, self._get_log_filename('ignored_keywords'))

        with open(ignored_file, 'w', encoding='utf-8') as f:
            f.write("# 被忽略的关键词日志\n")
            f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
            f.write(f"# 总计: {len(self.ignored_keywords)} 个关键词被忽略\n\n")

            # 按类别分组
            categories = {}
            for entry in self.ignored_keywords:
                category = entry['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(entry)

            for category, entries in categories.items():
                f.write(f"## {category} ({len(entries)} 个)\n")
                for entry in entries:
                    f.write(f"- {entry['keyword']} (原因: {entry['reason']})")
                    if entry.get('file_path'):
                        f.write(f" [文件: {entry['file_path']}]")
                    f.write(f" [{entry['timestamp']}]\n")
                f.write("\n")

        self.logger.info(f"导出被忽略关键词文件: {ignored_file}")

    def _export_error_trace(self):
        """导出错误追踪日志文件"""
        error_file = os.path.join(self.output_dir, self._get_log_filename('error_trace'))

        with open(error_file, 'w', encoding='utf-8') as f:
            f.write("# 错误追踪日志\n")
            f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
            f.write(f"# 总计: {len(self.error_log)} 条记录\n\n")

            for entry in self.error_log:
                f.write(f"[{entry['timestamp']}] {entry['level']}: {entry['message']}\n")
                if entry.get('error_code'):
                    f.write(f"  错误代码: {entry['error_code']}\n")
                if entry.get('file_path'):
                    f.write(f"  文件路径: {entry['file_path']}\n")
                if entry.get('context'):
                    f.write(f"  上下文: {json.dumps(entry['context'], ensure_ascii=False)}\n")
                f.write("\n")

        self.logger.info(f"导出错误追踪日志: {error_file}")

    def _export_keyword_lists(self):
        """导出各种关键词列表文件"""
        # 导出框架关键词
        if self.framework_keywords:
            framework_file = os.path.join(self.output_dir, self._get_log_filename('framework_keywords'))
            with open(framework_file, 'w', encoding='utf-8') as f:
                f.write("# 项目框架关键词列表\n")
                f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"# 总计: {len(self.framework_keywords)} 个关键词\n\n")
                for keyword in sorted(self.framework_keywords):
                    f.write(f"{keyword}\n")
            self.logger.info(f"导出框架关键词文件: {framework_file}")

        # 导出字符串关键词
        if self.string_keywords:
            string_file = os.path.join(self.output_dir, self._get_log_filename('string_keywords'))
            with open(string_file, 'w', encoding='utf-8') as f:
                f.write("# 项目字符串关键词列表\n")
                f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"# 总计: {len(self.string_keywords)} 个关键词\n\n")
                for keyword in sorted(self.string_keywords):
                    f.write(f"{keyword}\n")
            self.logger.info(f"导出字符串关键词文件: {string_file}")

        # 导出常量字符串
        if self.constant_strings:
            const_file = os.path.join(self.output_dir, self._get_log_filename('constant_strings'))
            with open(const_file, 'w', encoding='utf-8') as f:
                f.write("# 工程中常量字符串列表\n")
                f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"# 总计: {len(self.constant_strings)} 个字符串\n\n")
                for string in sorted(self.constant_strings):
                    f.write(f"{string}\n")
            self.logger.info(f"导出常量字符串文件: {const_file}")

        # 导出所有字符串
        if self.all_strings:
            all_strings_file = os.path.join(self.output_dir, self._get_log_filename('all_strings'))
            with open(all_strings_file, 'w', encoding='utf-8') as f:
                f.write("# 工程中所有字符串列表\n")
                f.write(f"# 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"# 总计: {len(self.all_strings)} 个字符串\n\n")
                for string in sorted(self.all_strings):
                    f.write(f"{string}\n")
            self.logger.info(f"导出所有字符串文件: {all_strings_file}")

    def _export_statistics_report(self):
        """导出统计报告"""
        stats = self.calculate_statistics()
        stats_file = os.path.join(self.output_dir, "obfuscation_statistics.json")

        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        # 同时生成可读的文本报告
        text_report_file = os.path.join(self.output_dir, "obfuscation_report.txt")
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write("iMix iOS混淆工具 - 处理报告\n")
            f.write("=" * 50 + "\n\n")

            # 总体统计
            f.write("总体统计:\n")
            for key, value in stats['总体统计'].items():
                f.write(f"  {key}: {value}\n")
            f.write("\n")

            # 各类型统计
            f.write("各类型统计:\n")
            for category, data in stats['各类型统计'].items():
                f.write(f"  {category}:\n")
                for key, value in data.items():
                    f.write(f"    {key}: {value}\n")
            f.write("\n")

        self.logger.info(f"导出统计报告: {stats_file}, {text_report_file}")

    def get_mapping(self, category: str, original: str) -> Optional[str]:
        """
        获取指定原始名称的混淆名称

        Args:
            category: 类别
            original: 原始名称

        Returns:
            混淆后的名称，如果不存在则返回None
        """
        return self.mapping_log.get(category, {}).get(original)

    def get_reverse_mapping(self, category: str, obfuscated: str) -> Optional[str]:
        """
        根据混淆名称查找原始名称 (反向查找)

        Args:
            category: 类别
            obfuscated: 混淆后的名称

        Returns:
            原始名称，如果不存在则返回None
        """
        mappings = self.mapping_log.get(category, {})
        for original, obf in mappings.items():
            if obf == obfuscated:
                return original
        return None

    def get_category_mappings(self, category: str) -> Dict[str, str]:
        """
        获取指定类别的所有映射关系

        Args:
            category: 类别

        Returns:
            该类别的映射字典
        """
        return self.mapping_log.get(category, {})

    def get_total_mappings_count(self) -> int:
        """获取总映射数量"""
        return sum(len(mappings) for mappings in self.mapping_log.values())

    def __str__(self) -> str:
        """返回日志器的字符串表示"""
        return f"ObfuscationLogger(output_dir={self.output_dir})"

    def __repr__(self) -> str:
        """返回日志器的详细字符串表示"""
        total_mappings = self.get_total_mappings_count()
        return f"ObfuscationLogger(output_dir={self.output_dir}, mappings={total_mappings})"
