"""
基础日志管理器

提供统一的日志记录功能，支持不同级别的日志输出。
基于ARCHITECTURE.md中的日志系统设计实现。
"""

import os
import logging
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path


class Logger:
    """
    基础日志管理器类
    
    功能:
    - 提供统一的日志记录接口
    - 支持多种日志级别 (DEBUG, INFO, WARNING, ERROR)
    - 支持文件和控制台输出
    - 支持日志格式自定义
    """
    
    def __init__(self, name: str = "iMix", log_level: str = "INFO", 
                 log_file: Optional[str] = None, console_output: bool = True):
        """
        初始化日志管理器
        
        Args:
            name: 日志器名称
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            log_file: 日志文件路径，如果为None则不输出到文件
            console_output: 是否输出到控制台
        """
        self.name = name
        self.log_level = log_level
        self.log_file = log_file
        self.console_output = console_output
        
        # 创建日志器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self._get_log_level(log_level))
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 设置日志格式
        self.formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加处理器
        self._setup_handlers()
    
    def _get_log_level(self, level_str: str) -> int:
        """
        将字符串日志级别转换为logging模块的级别常量
        
        Args:
            level_str: 日志级别字符串
            
        Returns:
            日志级别常量
        """
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        return level_map.get(level_str.upper(), logging.INFO)
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        if self.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(self.formatter)
            self.logger.addHandler(console_handler)
        
        # 文件处理器
        if self.log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setFormatter(self.formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(message, **kwargs)
    
    def log(self, level: str, message: str, **kwargs):
        """
        记录指定级别的日志
        
        Args:
            level: 日志级别
            message: 日志消息
        """
        log_level = self._get_log_level(level)
        self.logger.log(log_level, message, **kwargs)
    
    def set_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 新的日志级别
        """
        self.log_level = level
        self.logger.setLevel(self._get_log_level(level))
    
    def add_file_handler(self, file_path: str):
        """
        添加文件处理器
        
        Args:
            file_path: 日志文件路径
        """
        # 确保日志目录存在
        log_dir = os.path.dirname(file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(file_path, encoding='utf-8')
        file_handler.setFormatter(self.formatter)
        self.logger.addHandler(file_handler)
    
    def remove_handlers(self):
        """移除所有处理器"""
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
            handler.close()
    
    def get_log_file_path(self) -> Optional[str]:
        """获取当前日志文件路径"""
        return self.log_file
    
    def __str__(self) -> str:
        """返回日志器的字符串表示"""
        return f"Logger(name={self.name}, level={self.log_level})"
    
    def __repr__(self) -> str:
        """返回日志器的详细字符串表示"""
        return f"Logger(name={self.name}, level={self.log_level}, file={self.log_file})"


class ProgressLogger:
    """
    进度日志记录器
    
    专门用于记录混淆过程的进度信息，提供友好的进度显示。
    """
    
    def __init__(self, logger: Logger, total_steps: int = 0):
        """
        初始化进度日志记录器
        
        Args:
            logger: 基础日志器
            total_steps: 总步骤数
        """
        self.logger = logger
        self.total_steps = total_steps
        self.current_step = 0
        self.step_start_time = None
    
    def start_process(self, process_name: str, total_steps: int = None):
        """
        开始一个新的处理过程
        
        Args:
            process_name: 过程名称
            total_steps: 总步骤数
        """
        if total_steps:
            self.total_steps = total_steps
        self.current_step = 0
        self.step_start_time = datetime.now()
        
        self.logger.info(f"开始 {process_name}")
        if self.total_steps > 0:
            self.logger.info(f"总共 {self.total_steps} 个步骤")
    
    def start_step(self, step_name: str):
        """
        开始新的处理步骤
        
        Args:
            step_name: 步骤名称
        """
        self.current_step += 1
        self.step_start_time = datetime.now()
        
        if self.total_steps > 0:
            progress = (self.current_step / self.total_steps) * 100
            self.logger.info(f"[{self.current_step}/{self.total_steps}] ({progress:.1f}%) {step_name}...")
        else:
            self.logger.info(f"[{self.current_step}] {step_name}...")
    
    def update_substep(self, substep_name: str, count: Optional[int] = None):
        """
        更新子步骤进度
        
        Args:
            substep_name: 子步骤名称
            count: 处理数量
        """
        if count is not None:
            self.logger.info(f"  └─ {substep_name} ({count} 项)")
        else:
            self.logger.info(f"  └─ {substep_name}")
    
    def complete_step(self, step_name: str, result_count: Optional[int] = None):
        """
        完成当前步骤
        
        Args:
            step_name: 步骤名称
            result_count: 处理结果数量
        """
        if self.step_start_time:
            duration = (datetime.now() - self.step_start_time).total_seconds()
            if result_count is not None:
                self.logger.info(f"  ✅ {step_name} 完成 - 处理了 {result_count} 项 (耗时: {duration:.2f}s)")
            else:
                self.logger.info(f"  ✅ {step_name} 完成 (耗时: {duration:.2f}s)")
        else:
            if result_count is not None:
                self.logger.info(f"  ✅ {step_name} 完成 - 处理了 {result_count} 项")
            else:
                self.logger.info(f"  ✅ {step_name} 完成")
    
    def complete_process(self, process_name: str, total_results: Optional[Dict[str, int]] = None):
        """
        完成整个处理过程
        
        Args:
            process_name: 过程名称
            total_results: 总结果统计
        """
        self.logger.info(f"🎉 {process_name} 全部完成!")
        
        if total_results:
            self.logger.info("处理结果统计:")
            for key, count in total_results.items():
                self.logger.info(f"  - {key}: {count} 项")
    
    def log_error(self, error_message: str, context: Optional[str] = None):
        """
        记录错误信息
        
        Args:
            error_message: 错误消息
            context: 错误上下文
        """
        if context:
            self.logger.error(f"❌ 错误 [{context}]: {error_message}")
        else:
            self.logger.error(f"❌ 错误: {error_message}")
    
    def log_warning(self, warning_message: str, context: Optional[str] = None):
        """
        记录警告信息
        
        Args:
            warning_message: 警告消息
            context: 警告上下文
        """
        if context:
            self.logger.warning(f"⚠️  警告 [{context}]: {warning_message}")
        else:
            self.logger.warning(f"⚠️  警告: {warning_message}")


# 创建默认的全局日志器实例
default_logger = Logger("iMix")
progress_logger = ProgressLogger(default_logger)
