"""
日志系统模块

包含混淆过程的日志记录和管理功能:
- ObfuscationLogger: 混淆日志记录器
- logger: 基础日志管理器
- gui_logger: GUI日志接口 (预留)

日志文件类型:
- 被忽略的关键词日志.txt: 记录所有被过滤掉的关键词及原因
- 工程中常量字符串列表.txt: 项目中提取的所有常量字符串
- 工程中所有字符串列表.txt: 项目中提取的所有字符串内容
- project_framework_keywords.txt: 项目framework关键词
- project_all_string_words.txt: 项目字符串关键词
- obfuscation_mapping.json: 混淆映射关系
- error_trace.log: 错误追踪日志
- process.log: 处理过程日志
- obfuscation.log: 统一混淆日志
"""

# 导入日志类
from .logger import Logger, ProgressLogger, default_logger, progress_logger
from .obfuscation_logger import ObfuscationLogger
from .gui_logger import GUILoggerInterface, GUILogAdapter, default_gui_logger

__all__ = [
    "Logger",
    "ProgressLogger",
    "ObfuscationLogger",
    "GUILoggerInterface",
    "GUILogAdapter",
    "default_logger",
    "progress_logger",
    "default_gui_logger"
]
