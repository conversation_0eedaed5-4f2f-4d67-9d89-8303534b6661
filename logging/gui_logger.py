"""
GUI日志接口 (预留)

为GUI界面提供日志显示接口，支持实时日志更新和进度显示。
注意: 当前阶段专注于CLI实现，GUI功能暂时预留。
"""

from typing import Callable, Optional, Dict, Any
from datetime import datetime

from .logger import Logger


class GUILoggerInterface:
    """
    GUI日志接口类 (预留)
    
    功能:
    - 向GUI发送日志消息
    - 更新GUI进度显示
    - 提供日志级别过滤
    - 支持日志消息格式化
    
    注意: 这是一个预留接口，等CLI测试通过后再实现具体的GUI功能
    """
    
    def __init__(self, callback_func: Optional[Callable] = None):
        """
        初始化GUI日志接口
        
        Args:
            callback_func: GUI回调函数，用于接收日志消息
        """
        self.callback = callback_func
        self.enabled = callback_func is not None
        self.message_buffer = []  # 消息缓冲区
        self.max_buffer_size = 1000  # 最大缓冲区大小
        
        # 创建基础日志器
        self.logger = Logger("GUILogger", console_output=False)
    
    def set_callback(self, callback_func: Callable):
        """
        设置GUI回调函数
        
        Args:
            callback_func: 回调函数，接收 (message, level, timestamp) 参数
        """
        self.callback = callback_func
        self.enabled = True
        
        # 如果有缓冲的消息，发送给GUI
        self._flush_buffer()
    
    def emit_log(self, message: str, level: str = "INFO", 
                context: Optional[Dict[str, Any]] = None):
        """
        向GUI发送日志消息
        
        Args:
            message: 日志消息
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            context: 上下文信息 (可选)
        """
        timestamp = datetime.now()
        
        # 格式化消息
        formatted_message = self._format_message(message, level, timestamp, context)
        
        if self.enabled and self.callback:
            try:
                # 调用GUI回调函数
                self.callback(formatted_message, level, timestamp)
            except Exception as e:
                # 如果GUI回调失败，记录到基础日志器
                self.logger.error(f"GUI回调失败: {e}")
        else:
            # 如果GUI不可用，缓存消息
            self._buffer_message(formatted_message, level, timestamp)
    
    def emit_progress(self, current: int, total: int, message: str = ""):
        """
        向GUI发送进度更新
        
        Args:
            current: 当前进度
            total: 总进度
            message: 进度消息 (可选)
        """
        if self.enabled and self.callback:
            try:
                progress_data = {
                    'type': 'progress',
                    'current': current,
                    'total': total,
                    'percentage': (current / total * 100) if total > 0 else 0,
                    'message': message,
                    'timestamp': datetime.now()
                }
                self.callback(progress_data, 'PROGRESS', datetime.now())
            except Exception as e:
                self.logger.error(f"GUI进度更新失败: {e}")
    
    def emit_status(self, status: str, details: Optional[Dict[str, Any]] = None):
        """
        向GUI发送状态更新
        
        Args:
            status: 状态信息
            details: 详细信息 (可选)
        """
        if self.enabled and self.callback:
            try:
                status_data = {
                    'type': 'status',
                    'status': status,
                    'details': details,
                    'timestamp': datetime.now()
                }
                self.callback(status_data, 'STATUS', datetime.now())
            except Exception as e:
                self.logger.error(f"GUI状态更新失败: {e}")
    
    def _format_message(self, message: str, level: str, timestamp: datetime, 
                       context: Optional[Dict[str, Any]] = None) -> str:
        """
        格式化日志消息
        
        Args:
            message: 原始消息
            level: 日志级别
            timestamp: 时间戳
            context: 上下文信息
            
        Returns:
            格式化后的消息
        """
        formatted = f"[{timestamp.strftime('%H:%M:%S')}] {level}: {message}"
        
        if context:
            # 添加上下文信息
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            formatted += f" ({context_str})"
        
        return formatted
    
    def _buffer_message(self, message: str, level: str, timestamp: datetime):
        """
        缓存消息到缓冲区
        
        Args:
            message: 消息内容
            level: 日志级别
            timestamp: 时间戳
        """
        self.message_buffer.append({
            'message': message,
            'level': level,
            'timestamp': timestamp
        })
        
        # 如果缓冲区超过最大大小，移除最旧的消息
        if len(self.message_buffer) > self.max_buffer_size:
            self.message_buffer.pop(0)
    
    def _flush_buffer(self):
        """将缓冲区中的消息发送给GUI"""
        if not self.enabled or not self.callback:
            return
        
        for buffered_msg in self.message_buffer:
            try:
                self.callback(
                    buffered_msg['message'],
                    buffered_msg['level'],
                    buffered_msg['timestamp']
                )
            except Exception as e:
                self.logger.error(f"发送缓冲消息失败: {e}")
        
        # 清空缓冲区
        self.message_buffer.clear()
    
    def clear_buffer(self):
        """清空消息缓冲区"""
        self.message_buffer.clear()
    
    def get_buffer_size(self) -> int:
        """获取当前缓冲区大小"""
        return len(self.message_buffer)
    
    def set_max_buffer_size(self, size: int):
        """
        设置最大缓冲区大小
        
        Args:
            size: 最大缓冲区大小
        """
        self.max_buffer_size = size
        
        # 如果当前缓冲区超过新的最大大小，截断
        if len(self.message_buffer) > size:
            self.message_buffer = self.message_buffer[-size:]
    
    def disable(self):
        """禁用GUI日志接口"""
        self.enabled = False
        self.callback = None
    
    def enable(self, callback_func: Callable):
        """
        启用GUI日志接口
        
        Args:
            callback_func: GUI回调函数
        """
        self.set_callback(callback_func)
    
    def __str__(self) -> str:
        """返回接口的字符串表示"""
        status = "enabled" if self.enabled else "disabled"
        return f"GUILoggerInterface(status={status})"
    
    def __repr__(self) -> str:
        """返回接口的详细字符串表示"""
        status = "enabled" if self.enabled else "disabled"
        buffer_size = len(self.message_buffer)
        return f"GUILoggerInterface(status={status}, buffer_size={buffer_size})"


# 创建默认的GUI日志接口实例 (预留)
default_gui_logger = GUILoggerInterface()


# GUI日志适配器类 (预留)
class GUILogAdapter:
    """
    GUI日志适配器 (预留)
    
    将标准的日志记录器适配到GUI接口，提供统一的日志处理。
    """
    
    def __init__(self, gui_interface: GUILoggerInterface, base_logger: Logger):
        """
        初始化GUI日志适配器
        
        Args:
            gui_interface: GUI日志接口
            base_logger: 基础日志记录器
        """
        self.gui_interface = gui_interface
        self.base_logger = base_logger
    
    def log(self, level: str, message: str, context: Optional[Dict[str, Any]] = None):
        """
        记录日志到GUI和基础日志器
        
        Args:
            level: 日志级别
            message: 日志消息
            context: 上下文信息
        """
        # 记录到基础日志器
        self.base_logger.log(level, message)
        
        # 发送到GUI
        self.gui_interface.emit_log(message, level, context)
    
    def debug(self, message: str, context: Optional[Dict[str, Any]] = None):
        """记录DEBUG级别日志"""
        self.log("DEBUG", message, context)
    
    def info(self, message: str, context: Optional[Dict[str, Any]] = None):
        """记录INFO级别日志"""
        self.log("INFO", message, context)
    
    def warning(self, message: str, context: Optional[Dict[str, Any]] = None):
        """记录WARNING级别日志"""
        self.log("WARNING", message, context)
    
    def error(self, message: str, context: Optional[Dict[str, Any]] = None):
        """记录ERROR级别日志"""
        self.log("ERROR", message, context)
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        self.gui_interface.emit_progress(current, total, message)
    
    def update_status(self, status: str, details: Optional[Dict[str, Any]] = None):
        """更新状态"""
        self.gui_interface.emit_status(status, details)
