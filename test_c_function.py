#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行测试脚本
"""

import os
import sys
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试导入"""
    print("=== 测试导入 ===")
    try:
        from ObjectiveC.oc_custom.custom_pbxproj_add import SmartSDKAdder
        print("✅ 成功导入 SmartSDKAdder")
    except Exception as e:
        print(f"❌ 导入 SmartSDKAdder 失败: {e}")
        traceback.print_exc()
    
    try:
        from ObjectiveC.oc_custom.custom_pbxproj_clean import DemoProjectCleanerSafe
        print("✅ 成功导入 DemoProjectCleanerSafe")
    except Exception as e:
        print(f"❌ 导入 DemoProjectCleanerSafe 失败: {e}")
        traceback.print_exc()

def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    # 测试路径处理
    home_dir = os.path.expanduser("~")
    print(f"用户主目录: {home_dir}")
    
    # 测试文件检查
    test_files = [
        "oc_cli.py",
        "oc_gui.py",
        "ObjectiveC/oc_custom/custom_pbxproj_add.py",
        "ObjectiveC/oc_custom/custom_pbxproj_clean.py"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")

def test_cli_help():
    """测试CLI帮助"""
    print("\n=== 测试CLI帮助 ===")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "oc_cli.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        print(f"CLI帮助命令退出码: {result.returncode}")
        if result.stdout:
            print("标准输出:")
            print(result.stdout[:500] + ("..." if len(result.stdout) > 500 else ""))
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 测试CLI帮助失败: {e}")
        traceback.print_exc()

def test_gui_imports():
    """测试GUI相关导入"""
    print("\n=== 测试GUI相关导入 ===")
    try:
        import tkinter as tk
        print("✅ 成功导入 tkinter")
        
        from tkinter import ttk, filedialog, messagebox
        print("✅ 成功导入 tkinter 子模块")
        
        # 测试创建根窗口（但不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        print("✅ 成功创建 tkinter 根窗口")
        root.destroy()
        
    except Exception as e:
        print(f"❌ GUI导入测试失败: {e}")
        traceback.print_exc()

def test_pbxproj_classes():
    """测试pbxproj类的基本功能"""
    print("\n=== 测试pbxproj类 ===")
    try:
        from ObjectiveC.oc_custom.custom_pbxproj_add import SmartSDKAdder
        from ObjectiveC.oc_custom.custom_pbxproj_clean import DemoProjectCleanerSafe
        
        # 测试创建实例（不执行实际操作）
        print("测试SmartSDKAdder...")
        fake_pbx_path = "/fake/path/project.pbxproj"
        fake_sdk_path = "/fake/path/sdk"
        
        # 这里只是测试类能否正常初始化，不执行实际操作
        print("✅ SmartSDKAdder 类可用")
        
        print("测试DemoProjectCleanerSafe...")
        print("✅ DemoProjectCleanerSafe 类可用")
        
    except Exception as e:
        print(f"❌ pbxproj类测试失败: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    print("开始命令行测试...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print()
    
    test_imports()
    test_file_operations()
    test_cli_help()
    test_gui_imports()
    test_pbxproj_classes()
    
    print("\n=== 测试完成 ===")
    print("如果上面都显示 ✅，说明基本功能正常")
    print("如果有 ❌，请检查对应的错误信息")

if __name__ == "__main__":
    main() 