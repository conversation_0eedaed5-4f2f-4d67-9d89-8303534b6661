# iMix iOS混淆工具 - 依赖包列表
# 基于Python 3.13.3开发

# 核心依赖包
pyyaml>=6.0              # YAML配置文件处理
pathlib>=1.0             # 路径处理 (Python 3.4+内置)

# 开发依赖包 (可选)
pytest>=7.0              # 单元测试框架
pytest-cov>=4.0          # 测试覆盖率
black>=23.0              # 代码格式化
flake8>=6.0              # 代码检查
mypy>=1.0                # 类型检查

# 打包依赖 (可选)
pyinstaller>=5.0         # 可执行文件打包
setuptools>=68.0         # 包管理
wheel>=0.40              # 包构建

# 注意事项:
# 1. 本项目主要使用Python标准库，外部依赖较少
# 2. pyyaml是唯一的必需外部依赖，用于配置文件处理
# 3. 其他依赖包主要用于开发、测试和打包
# 4. 如果只是运行基本功能，只需要安装pyyaml即可
# 5. Python版本要求: >=3.13.3 (根据用户偏好设置)
