# Objective-C代码混淆工具

专业的iOS工程代码混淆解决方案，支持类名、方法名、变量名等全方位混淆。

## 🚀 快速开始

### GUI版本（推荐）
```bash
# 直接运行GUI应用
open "dist/OC代码混淆工具.app"

# 或使用启动脚本（首次运行）
./scripts/启动GUI.sh
```

### CLI版本
```bash
# 命令行运行
./dist/oc_cli --input-path /path/to/your/project --sdk-region 2 --functions 0
```

## 📁 项目结构

```
00001/
├── README.md                    # 项目说明文档
├── 项目信息.txt                  # 快速项目信息
├── .gitignore                   # Git忽略规则
├── oc_gui.py                    # GUI应用主程序
├── oc_cli.py                    # CLI命令行工具
├── requirements.txt             # Python依赖包列表
├── ObjectiveC/                  # 核心混淆引擎
│   ├── oc_config.yaml          # 混淆配置文件
│   ├── oc_*.py                 # 混淆核心模块
│   └── oc_function/            # 功能模块
├── 配置文件/                    # 混淆配置模板
│   └── ObjectiveC配置文件/      # OC配置文件
├── backend_examples/            # 后端集成示例
│   ├── nodejs_example.js       # Node.js集成示例
│   └── php_example.php         # PHP集成示例
├── docs/                       # 项目文档
│   ├── 打包使用指南.md          # 打包使用说明
│   ├── GUI使用说明.md          # GUI使用指南
│   ├── CLI_使用说明.md         # CLI使用指南
│   └── 其他文档...
├── scripts/                    # 构建脚本
│   ├── build_gui.sh           # GUI打包脚本
│   ├── build_cli.sh           # CLI打包脚本
│   ├── build_universal.py     # 通用构建工具
│   ├── 安装依赖包.sh            # 依赖安装脚本
│   ├── 启动GUI.sh             # GUI启动脚本
│   └── 其他脚本...
├── dist/                       # 打包产物
│   ├── OC代码混淆工具.app      # macOS GUI应用
│   ├── OC代码混淆工具          # 跨平台GUI可执行文件
│   └── oc_cli                  # CLI可执行文件
├── venv/                       # Python虚拟环境
└── 混淆完成后的文件/            # 混淆输出目录
```

## 🛠️ 开发环境设置

1. **安装依赖**
   ```bash
   ./scripts/安装依赖包.sh
   ```

2. **激活虚拟环境**
   ```bash
   source venv/bin/activate
   ```

3. **运行开发版本**
   ```bash
   # GUI版本
   python oc_gui.py
   
   # CLI版本
   python oc_cli.py --help
   
   # 使用启动脚本（推荐）
   ./scripts/启动GUI.sh
   ```

## 📦 打包部署

### GUI应用打包
```bash
./scripts/build_gui.sh
```

### CLI工具打包
```bash
# macOS版本
./scripts/build_cli.sh

# Linux版本 (在Linux系统上运行)
./scripts/deploy-linux.sh

# Linux版本 (在macOS上使用Docker CentOS 7环境构建，推荐)
./scripts/build-docker-centos7.sh

# Linux版本 (在macOS上使用Docker通用环境构建)
./scripts/build-linux-docker.sh
```

### 一键打包所有
```bash
./scripts/快速打包.sh
```

### 跨平台构建

不同操作系统需要在对应平台上构建：

| 构建平台 | 目标平台 | 方法 | 输出文件 |
|---------|---------|------|---------|
| macOS | macOS | `./scripts/build_cli.sh` | `dist/oc_cli` |
| Linux | Linux | `./scripts/deploy-linux.sh` | `dist/oc_cli` |
| macOS | Linux | `./scripts/build-docker-centos7.sh` | `dist-centos7/oc_cli` |
| macOS | Linux (通用) | `./scripts/build-linux-docker.sh` | `dist-linux/oc_cli` |

## 🔧 功能特性

- ✅ **类名混淆** - 自动混淆Objective-C类名
- ✅ **方法名混淆** - 混淆实例方法和类方法
- ✅ **属性混淆** - 混淆@property声明的属性
- ✅ **变量名混淆** - 混淆局部变量和成员变量
- ✅ **文件名混淆** - 重命名.h和.m文件
- ✅ **文件夹混淆** - 重命名项目目录结构
- ✅ **字符串混淆** - 混淆代码中的字符串常量
- ✅ **SDK适配** - 支持主流第三方SDK
- ✅ **增量混淆** - 支持项目增量更新
- ✅ **GUI界面** - 现代化图形用户界面
- ✅ **CLI工具** - 支持命令行批处理

## 🌍 SDK支持

### 国内版SDK
- 友盟统计、极光推送、个推、腾讯Bugly等

### 海外版SDK  
- AppsFlyer、Facebook、Firebase、Adjust等

## 📋 使用示例

### GUI使用
1. 运行 `./scripts/启动GUI.sh` 或双击 `OC代码混淆工具.app`
2. 选择工程路径
3. 配置混淆选项
4. 点击"开始混淆"

### CLI使用
```bash
# 基础混淆
./dist/oc_cli --input-path /path/to/project --functions 0

# 海外SDK项目混淆
./dist/oc_cli --input-path /path/to/project --sdk-region 2 --sdk-options "AppsFlyer,Facebook,Firebase" --functions 0

# 指定新项目名
./dist/oc_cli --input-path /path/to/project --new-project-name "MyObfuscatedApp" --functions 0
```

## 🔗 后端集成

项目提供Node.js和PHP的后端集成示例，支持：
- 异步混淆处理
- 进度状态查询  
- 文件上传下载
- 错误处理

详见 `backend_examples/` 目录。

## 🛠️ 常用脚本

```bash
# 首次运行/环境设置
./scripts/安装依赖包.sh        # 安装依赖包
./scripts/启动GUI.sh          # 启动GUI应用

# 开发和构建
./scripts/build_gui.sh        # 打包GUI应用
./scripts/build_cli.sh        # 打包CLI工具
./scripts/快速打包.sh          # 一键打包所有

# 问题排查
./scripts/解决网络问题.sh      # 解决网络连接问题
```

## 📚 文档

- [GUI使用说明](docs/GUI使用说明.md)
- [CLI使用指南](docs/CLI_使用说明.md)
- [打包部署方案](docs/打包部署方案.md)
- [后端集成示例](backend_examples/)

## 🤝 技术支持

如有问题，请参考 `docs/` 目录下的相关文档或查看混淆日志。

## 📄 许可证

本项目为内部工具，仅供授权用户使用。 