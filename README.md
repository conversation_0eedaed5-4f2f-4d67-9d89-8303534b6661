# iMix - 通用iOS代码混淆工具

基于00001项目的核心逻辑，创建一个通用的iOS代码混淆工具，去除定制化内容，提供标准化的混淆解决方案。

## 🚀 主要功能

- **13种类型混淆**: 支持文件夹、文件、类名、属性、方法、变量、常量、枚举、代理、Block、C函数、字符串、图片等
- **完全复用00001逻辑**: 确保混淆效果与原项目完全一致
- **CLI优先设计**: 提供完整的命令行接口，预留GUI图形界面
- **工程名修改**: 支持自动生成或自定义工程名
- **图片MD5混淆**: 修改图片MD5值和名称，保持图片质量不变
- **详细日志记录**: 完整的混淆日志和映射关系记录
- **配置化管理**: 支持YAML配置文件，灵活控制混淆行为

## 📋 系统要求

- **Python版本**: >= 3.13.3
- **操作系统**: macOS, Linux, Windows
- **目标平台**: iOS项目 (.xcodeproj)
- **依赖包**: PyYAML (其他为Python标准库)

## 🛠️ 安装方法

### 方法1: 从源码安装

```bash
# 克隆项目
git clone <repository-url>
cd iMix

# 安装依赖
pip install -r requirements.txt

# 安装iMix
pip install -e .
```

### 方法2: 直接运行

```bash
# 安装依赖
pip install pyyaml

# 直接运行CLI
python cli/cli_main.py --help
```

## 🎯 快速开始

### 基础混淆

```bash
# 混淆iOS项目
imix obfuscate /path/to/your/ios/project

# 指定输出路径
imix obfuscate /path/to/ios/project --output-path /path/to/output

# 使用自定义配置
imix obfuscate /path/to/ios/project --config-file custom_config.yaml
```

### 高级选项

```bash
# 完整混淆 (包含工程名和图片)
imix obfuscate /path/to/ios/project \
  --rename-project \
  --custom-project-name "MyNewProject" \
  --obfuscate-images \
  --modify-image-md5

# 指定提取器
imix obfuscate /path/to/ios/project \
  --extractors "class,property,method,variable"

# 调试模式
imix obfuscate /path/to/ios/project \
  --log-level DEBUG \
  --dry-run
```

### 其他命令

```bash
# 验证项目结构
imix validate /path/to/ios/project

# 显示配置
imix config show

# 生成配置文件
imix config init --output my_config.yaml

# 清理临时文件
imix clean /path/to/ios/project

# 查看版本
imix --version
```

## 📁 项目结构

```
iMix/
├── core/                    # 核心处理模块
│   ├── processor.py         # 混淆处理器
│   ├── filter.py           # 过滤器系统
│   ├── generator.py        # 名称生成器
│   └── extractor.py        # 提取器注册系统
├── extractors/             # 各类型提取器
│   ├── base_extractor.py   # 提取器基类
│   ├── property_extractor.py  # 属性提取器
│   ├── method_extractor.py    # 方法提取器
│   └── ...                 # 其他提取器 (TODO)
├── config/                 # 配置系统
│   ├── config_manager.py   # 配置管理器
│   ├── default_config.yaml # 默认配置
│   └── keywords/           # 关键词库
├── utils/                  # 工具模块
├── logging/                # 日志系统
├── cli/                    # 命令行接口
├── gui/                    # 图形界面 (预留)
└── tests/                  # 测试模块
```

## ⚙️ 配置说明

iMix使用YAML格式的配置文件，主要配置项包括：

```yaml
# 项目过滤配置
project_filters:
  ignore_folders: ["Pods", ".git", "build"]
  ignore_files: ["AppDelegate.h", "AppDelegate.m"]

# 混淆规则配置
obfuscation_rules:
  preserve_init_methods: true
  preserve_delegate_methods: true
  min_name_length: 3
  max_name_length: 15

# 提取器配置
extractors:
  enabled: ["folder", "file", "class", "property", "method"]
  priorities:
    property_extractor: 100
    method_extractor: 40

# 输出配置
output:
  create_backup: true
  generate_mapping_log: true
  log_level: "INFO"
```

## 📊 混淆效果

### 提取器优先级 (按执行顺序)

1. **文件夹提取器** (优先级: 130) - 混淆目录结构
2. **文件提取器** (优先级: 120) - 混淆文件名
3. **类名提取器** (优先级: 110) - 混淆类定义
4. **属性提取器** (优先级: 100) - 混淆@property声明
5. **常量提取器** (优先级: 90) - 混淆常量定义
6. **变量提取器** (优先级: 80) - 混淆变量名
7. **枚举提取器** (优先级: 70) - 混淆枚举定义
8. **代理提取器** (优先级: 60) - 混淆@protocol协议
9. **Block提取器** (优先级: 50) - 混淆Block定义
10. **方法提取器** (优先级: 40) - 混淆方法名
11. **C函数提取器** (优先级: 30) - 混淆C函数
12. **字符串提取器** (优先级: 20) - 混淆字符串常量
13. **图片提取器** (优先级: 10) - 混淆图片资源

### 日志输出

混淆完成后会生成以下日志文件：

- `obfuscation_mapping.json` - 完整的映射关系
- `被忽略的关键词日志.txt` - 被过滤的关键词及原因
- `project_framework_keywords.txt` - 项目框架关键词
- `project_all_string_words.txt` - 项目字符串关键词
- `obfuscation.log` - 统一混淆日志
- `error_trace.log` - 错误追踪日志
- `process.log` - 处理过程日志

## 🔧 开发说明

### 当前实现状态

- ✅ **项目架构**: 完整的模块化架构设计
- ✅ **配置系统**: YAML配置文件管理
- ✅ **日志系统**: 完整的日志记录和统计
- ✅ **提取器框架**: 基类和注册系统
- ✅ **核心处理模块**: 过滤器、生成器、处理器
- ✅ **CLI接口**: 完整的命令行功能
- 🔄 **具体提取器**: 属性和方法提取器已实现，其他待完善
- 🔄 **GUI界面**: 预留，等CLI测试通过后实现

### 基于00001项目的实现原则

1. **完全复用**: 直接复用00001项目中对应模块的提取逻辑
2. **一模一样**: 确保提取效果与原项目完全一致
3. **不减少规则**: 不能删除或简化任何原有的提取规则
4. **可以优化**: 允许代码结构和性能优化，但逻辑结果必须相同

### TODO列表

- [ ] 完善各个提取器的具体实现 (基于00001项目)
- [ ] 实现文件替换和输出逻辑
- [ ] 添加工程名修改功能
- [ ] 实现图片MD5混淆功能
- [ ] 编写单元测试和集成测试
- [ ] 实现GUI图形界面
- [ ] 性能优化和错误处理完善

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进iMix。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 查看文档

---

**注意**: 当前版本为框架实现，具体的提取逻辑需要基于00001项目逐步完善。
