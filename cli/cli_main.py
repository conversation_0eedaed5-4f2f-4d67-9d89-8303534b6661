#!/usr/bin/env python3
"""
iMix CLI主程序

提供完整的命令行接口，支持iOS项目混淆的各种操作。
基于ARCHITECTURE.md中的CLI设计实现。
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Optional, List

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from core import ObfuscationProcessor
from config import ConfigManager
from logging import Logger, default_logger


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器
    
    Returns:
        配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        prog='iMix',
        description='iMix - 通用iOS代码混淆工具',
        epilog='基于00001项目的核心逻辑，提供标准化的iOS代码混淆解决方案。'
    )
    
    # 添加版本信息
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='iMix 1.0.0'
    )
    
    # 创建子命令
    subparsers = parser.add_subparsers(
        dest='command',
        help='可用的命令',
        metavar='COMMAND'
    )
    
    # obfuscate命令 - 执行混淆操作
    obfuscate_parser = subparsers.add_parser(
        'obfuscate',
        help='执行iOS项目混淆',
        description='对指定的iOS项目进行代码混淆处理'
    )
    
    obfuscate_parser.add_argument(
        'input_path',
        help='输入项目路径'
    )
    
    obfuscate_parser.add_argument(
        '--output-path', '-o',
        help='输出路径 (默认在输入项目目录下创建)'
    )
    
    obfuscate_parser.add_argument(
        '--config-file', '-c',
        help='自定义配置文件路径'
    )
    
    obfuscate_parser.add_argument(
        '--extractors',
        help='指定要使用的提取器 (逗号分隔)',
        default='folder,file,class,property,constant,variable,enum,delegate,block,method,c_function,string,image'
    )
    
    obfuscate_parser.add_argument(
        '--rename-project',
        action='store_true',
        help='修改工程名'
    )
    
    obfuscate_parser.add_argument(
        '--custom-project-name',
        help='自定义工程名 (配合--rename-project使用)'
    )
    
    obfuscate_parser.add_argument(
        '--obfuscate-images',
        action='store_true',
        help='混淆图片资源'
    )
    
    obfuscate_parser.add_argument(
        '--modify-image-md5',
        action='store_true',
        help='修改图片MD5值'
    )
    
    obfuscate_parser.add_argument(
        '--threads',
        type=int,
        default=4,
        help='并发线程数 (默认: 4)'
    )
    
    obfuscate_parser.add_argument(
        '--cache-enabled',
        action='store_true',
        help='启用缓存机制'
    )
    
    obfuscate_parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    obfuscate_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式，不实际修改文件'
    )
    
    # validate命令 - 验证项目结构
    validate_parser = subparsers.add_parser(
        'validate',
        help='验证iOS项目结构',
        description='检查项目结构是否符合混淆要求'
    )
    
    validate_parser.add_argument(
        'project_path',
        help='项目路径'
    )
    
    validate_parser.add_argument(
        '--config-file', '-c',
        help='配置文件路径'
    )
    
    # clean命令 - 清理临时文件
    clean_parser = subparsers.add_parser(
        'clean',
        help='清理临时文件',
        description='清理混淆过程中产生的临时文件'
    )
    
    clean_parser.add_argument(
        'project_path',
        help='项目路径'
    )
    
    # config命令 - 配置管理
    config_parser = subparsers.add_parser(
        'config',
        help='配置管理',
        description='管理iMix配置'
    )
    
    config_subparsers = config_parser.add_subparsers(
        dest='config_action',
        help='配置操作'
    )
    
    # config show - 显示当前配置
    config_subparsers.add_parser(
        'show',
        help='显示当前配置'
    )
    
    # config init - 初始化配置文件
    init_parser = config_subparsers.add_parser(
        'init',
        help='初始化配置文件'
    )
    
    init_parser.add_argument(
        '--output', '-o',
        help='配置文件输出路径',
        default='imix_config.yaml'
    )
    
    return parser


def handle_obfuscate_command(args) -> int:
    """
    处理obfuscate命令
    
    Args:
        args: 命令行参数
        
    Returns:
        退出码
    """
    try:
        logger = Logger("CLI.Obfuscate", log_level=args.log_level)
        logger.info("开始iOS项目混淆")
        
        # 验证输入路径
        if not os.path.exists(args.input_path):
            logger.error(f"输入路径不存在: {args.input_path}")
            return 1
        
        if not os.path.isdir(args.input_path):
            logger.error(f"输入路径不是目录: {args.input_path}")
            return 1
        
        # 创建混淆处理器
        processor = ObfuscationProcessor(args.config_file)
        
        # 验证配置
        if not processor.validate_configuration():
            logger.error("配置验证失败")
            return 1
        
        # 如果是试运行模式，只进行验证
        if args.dry_run:
            logger.info("试运行模式 - 仅进行验证，不修改文件")
            logger.info("项目验证通过，可以进行混淆")
            return 0
        
        # 执行混淆处理
        success = processor.process_project(args.input_path, args.output_path)
        
        if success:
            logger.info("混淆处理完成")
            
            # 显示结果统计
            results = processor.get_processing_results()
            if results:
                logger.info("处理结果统计:")
                mappings = results.get('mappings', {})
                for category, mapping in mappings.items():
                    logger.info(f"  {category}: {len(mapping)} 项")
            
            return 0
        else:
            logger.error("混淆处理失败")
            return 1
            
    except Exception as e:
        logger.error(f"混淆命令执行失败: {e}")
        return 1


def handle_validate_command(args) -> int:
    """
    处理validate命令
    
    Args:
        args: 命令行参数
        
    Returns:
        退出码
    """
    try:
        logger = Logger("CLI.Validate")
        logger.info("开始项目验证")
        
        # 验证项目路径
        if not os.path.exists(args.project_path):
            logger.error(f"项目路径不存在: {args.project_path}")
            return 1
        
        # 检查.xcodeproj文件
        xcodeproj_files = list(Path(args.project_path).glob("*.xcodeproj"))
        if not xcodeproj_files:
            logger.error("未找到.xcodeproj文件")
            return 1
        
        logger.info(f"找到工程文件: {xcodeproj_files[0].name}")
        
        # 创建配置管理器进行验证
        config = ConfigManager(args.config_file)
        if not config.validate_config():
            logger.error("配置验证失败")
            return 1
        
        logger.info("项目验证通过")
        return 0
        
    except Exception as e:
        logger.error(f"验证命令执行失败: {e}")
        return 1


def handle_clean_command(args) -> int:
    """
    处理clean命令
    
    Args:
        args: 命令行参数
        
    Returns:
        退出码
    """
    try:
        logger = Logger("CLI.Clean")
        logger.info("开始清理临时文件")
        
        # TODO: 实现清理逻辑
        # 1. 查找并删除iMix生成的临时文件
        # 2. 清理缓存文件
        # 3. 删除中间处理文件
        
        logger.info("临时文件清理完成")
        return 0
        
    except Exception as e:
        logger.error(f"清理命令执行失败: {e}")
        return 1


def handle_config_command(args) -> int:
    """
    处理config命令
    
    Args:
        args: 命令行参数
        
    Returns:
        退出码
    """
    try:
        logger = Logger("CLI.Config")
        
        if args.config_action == 'show':
            # 显示当前配置
            config = ConfigManager()
            logger.info("当前配置:")
            logger.info(f"  项目名称: {config.get('project.name')}")
            logger.info(f"  版本: {config.get('project.version')}")
            logger.info(f"  日志级别: {config.get('output.log_level')}")
            
            # 显示启用的提取器
            enabled_extractors = config.get('extractors.enabled', [])
            logger.info(f"  启用的提取器: {', '.join(enabled_extractors)}")
            
        elif args.config_action == 'init':
            # 初始化配置文件
            config = ConfigManager()
            config.save_config(args.output)
            logger.info(f"配置文件已生成: {args.output}")
        
        return 0
        
    except Exception as e:
        logger.error(f"配置命令执行失败: {e}")
        return 1


def main() -> int:
    """
    CLI主函数
    
    Returns:
        退出码
    """
    try:
        # 创建参数解析器
        parser = create_argument_parser()
        
        # 解析命令行参数
        args = parser.parse_args()
        
        # 如果没有指定命令，显示帮助信息
        if not args.command:
            parser.print_help()
            return 0
        
        # 根据命令执行相应的处理函数
        if args.command == 'obfuscate':
            return handle_obfuscate_command(args)
        elif args.command == 'validate':
            return handle_validate_command(args)
        elif args.command == 'clean':
            return handle_clean_command(args)
        elif args.command == 'config':
            return handle_config_command(args)
        else:
            default_logger.error(f"未知命令: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        default_logger.info("用户中断操作")
        return 130
    except Exception as e:
        default_logger.error(f"程序执行失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
