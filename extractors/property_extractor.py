"""
属性提取器

提取Objective-C中的@property声明的属性名。
基于00001项目的i_property.py逻辑实现。

注意: 当前为占位符实现，具体提取逻辑需要基于00001项目的custom_core.py完善。
"""

import re
from typing import List, Optional

from .base_extractor import BaseExtractor


class PropertyExtractor(BaseExtractor):
    """
    属性提取器类
    
    功能:
    - 提取@property声明的属性名
    - 支持各种属性修饰符 (nonatomic, strong, weak, copy等)
    - 处理属性的getter/setter方法名
    - 基于00001项目的提取逻辑实现
    
    TODO: 需要基于00001项目的custom_core.py中的属性提取逻辑完善实现
    """
    
    def __init__(self, name: str = "property", priority: int = 100, enabled: bool = True):
        """
        初始化属性提取器
        
        Args:
            name: 提取器名称
            priority: 优先级 (默认100，按架构文档中的优先级设置)
            enabled: 是否启用
        """
        super().__init__(name, priority, enabled)
        
        # 编译正则表达式以提高性能
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        # TODO: 这些是占位符模式，需要基于00001项目的custom_core.py中的正则表达式完善
        
        # 基础@property模式
        self.property_pattern = re.compile(
            r'@property\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;',
            re.MULTILINE
        )
        
        # 简单@property模式 (无修饰符)
        self.simple_property_pattern = re.compile(
            r'@property\s+([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;',
            re.MULTILINE
        )
        
        # IBOutlet属性模式
        self.iboutlet_pattern = re.compile(
            r'@property\s*\([^)]*\)\s*IBOutlet\s+([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;',
            re.MULTILINE
        )
    
    def get_extraction_patterns(self) -> List[str]:
        """
        获取提取用的正则表达式模式
        
        Returns:
            正则表达式模式列表
        """
        return [
            r'@property\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;',
            r'@property\s+([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;',
            r'@property\s*\([^)]*\)\s*IBOutlet\s+([A-Za-z_][A-Za-z0-9_]*\s*\*?\s*)?([A-Za-z_][A-Za-z0-9_]*)\s*;'
        ]
    
    def extract(self, content: str, file_path: Optional[str] = None) -> List[str]:
        """
        从内容中提取属性名
        
        Args:
            content: 文件内容
            file_path: 文件路径 (可选)
            
        Returns:
            提取到的属性名列表
        """
        properties = []
        
        try:
            # TODO: 这是占位符实现，需要基于00001项目的custom_core.py中的属性提取逻辑完善
            
            # 1. 提取标准@property声明
            matches = self.property_pattern.findall(content)
            for match in matches:
                if len(match) >= 2:
                    property_name = match[1].strip()
                    if self.validate_extracted_item(property_name):
                        properties.append(property_name)
            
            # 2. 提取简单@property声明
            matches = self.simple_property_pattern.findall(content)
            for match in matches:
                if len(match) >= 2:
                    property_name = match[1].strip()
                    if self.validate_extracted_item(property_name):
                        properties.append(property_name)
            
            # 3. 提取IBOutlet属性
            matches = self.iboutlet_pattern.findall(content)
            for match in matches:
                if len(match) >= 2:
                    property_name = match[1].strip()
                    if self.validate_extracted_item(property_name):
                        properties.append(property_name)
            
            # 去重
            properties = list(set(properties))
            
            if file_path:
                self.logger.debug(f"从文件 {file_path} 提取到 {len(properties)} 个属性")
            
            return properties
            
        except Exception as e:
            self.logger.error(f"属性提取失败: {e}")
            if file_path:
                self.logger.error(f"文件: {file_path}")
            return []
    
    def validate_extracted_item(self, item: str) -> bool:
        """
        验证提取到的属性名是否有效
        
        Args:
            item: 属性名
            
        Returns:
            是否有效
        """
        # 调用基类的基础验证
        if not super().validate_extracted_item(item):
            return False
        
        # 属性名特定验证
        # 1. 不能以数字开头
        if item and item[0].isdigit():
            return False
        
        # 2. 只能包含字母、数字和下划线
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', item):
            return False
        
        # 3. 不能是常见的系统属性名 (这里只是示例，实际应该从配置中读取)
        system_properties = {
            'delegate', 'dataSource', 'target', 'action', 'tag',
            'frame', 'bounds', 'center', 'transform', 'alpha',
            'hidden', 'userInteractionEnabled', 'backgroundColor'
        }
        
        if item in system_properties:
            return False
        
        return True
    
    def get_category(self) -> str:
        """获取提取器类别名称"""
        return "properties"
    
    def get_file_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.h', '.m', '.mm']
    
    def extract_property_details(self, content: str) -> List[dict]:
        """
        提取属性的详细信息 (扩展功能)
        
        Args:
            content: 文件内容
            
        Returns:
            属性详细信息列表，每个元素包含属性名、类型、修饰符等
            
        TODO: 这是扩展功能，可以在基础提取完成后实现
        """
        # 占位符实现
        return []
    
    def __str__(self) -> str:
        """返回提取器的字符串表示"""
        return f"PropertyExtractor(priority={self.priority}, enabled={self.enabled})"


# 注意事项:
# 1. 当前实现是占位符，主要用于建立框架结构
# 2. 具体的提取逻辑需要基于00001项目的custom_core.py中的属性提取代码完善
# 3. 正则表达式模式需要完全复用00001项目的模式，确保提取效果一致
# 4. 过滤逻辑需要配合FilterSystem使用，基于oc_tool.py的is_word_can_mix逻辑
# 5. 名称生成需要配合NameGenerator使用，基于oc_tool.py的random_one_string逻辑
