"""
提取器模块

包含各种类型的代码元素提取器，按优先级顺序:
1. folder_extractor - 文件夹提取器
2. file_extractor - 文件名提取器  
3. class_extractor - 类名提取器
4. property_extractor - 属性提取器
5. constant_extractor - 常量提取器
6. variable_extractor - 变量提取器
7. enum_extractor - 枚举提取器
8. delegate_extractor - 代理提取器
9. block_extractor - Block提取器
10. method_extractor - 方法提取器
11. c_function_extractor - C函数提取器
12. string_extractor - 字符串提取器
13. image_extractor - 图片资源提取器

所有提取器都基于BaseExtractor基类实现，确保统一的接口和行为。
"""

# 导入基类和已实现的提取器
from .base_extractor import BaseExtractor
from .property_extractor import PropertyExtractor
from .method_extractor import MethodExtractor

# TODO: 其他提取器待实现
# from .folder_extractor import FolderExtractor
# from .file_extractor import FileExtractor
# from .class_extractor import ClassExtractor
# from .constant_extractor import ConstantExtractor
# from .variable_extractor import VariableExtractor
# from .enum_extractor import EnumExtractor
# from .delegate_extractor import DelegateExtractor
# from .block_extractor import BlockExtractor
# from .c_function_extractor import CFunctionExtractor
# from .string_extractor import StringExtractor
# from .image_extractor import ImageExtractor

# 提取器优先级配置 (按照用户指定顺序)
EXTRACTOR_PRIORITIES = {
    'folder_extractor': 130,       # 1. 文件夹 (最高优先级)
    'file_extractor': 120,         # 2. 文件名
    'class_extractor': 110,        # 3. 类名
    'property_extractor': 100,     # 4. 属性名
    'constant_extractor': 90,      # 5. 常量名
    'variable_extractor': 80,      # 6. 变量名
    'enum_extractor': 70,          # 7. 枚举
    'delegate_extractor': 60,      # 8. 代理
    'block_extractor': 50,         # 9. Block
    'method_extractor': 40,        # 10. 方法名
    'c_function_extractor': 30,    # 11. C函数
    'string_extractor': 20,        # 12. 字符串
    'image_extractor': 10          # 13. 图片资源 (最低优先级)
}

__all__ = [
    "BaseExtractor",
    "PropertyExtractor",
    "MethodExtractor",
    # TODO: 其他提取器待实现
    # "FolderExtractor",
    # "FileExtractor",
    # "ClassExtractor",
    # "ConstantExtractor",
    # "VariableExtractor",
    # "EnumExtractor",
    # "DelegateExtractor",
    # "BlockExtractor",
    # "CFunctionExtractor",
    # "StringExtractor",
    # "ImageExtractor",
    "EXTRACTOR_PRIORITIES"
]
