"""
提取器基类

定义所有提取器的统一接口和基础功能。
基于ARCHITECTURE.md中的提取器设计实现。
"""

import re
from abc import ABC, abstractmethod
from typing import List, Dict, Set, Optional, Tuple, Any
from pathlib import Path

from ..logging import Logger


class BaseExtractor(ABC):
    """
    提取器基类
    
    所有具体的提取器都必须继承此基类并实现抽象方法。
    提供统一的接口和基础功能，确保所有提取器的行为一致。
    
    基于00001项目的提取逻辑设计，完全复用核心算法。
    """
    
    def __init__(self, name: str, priority: int = 0, enabled: bool = True):
        """
        初始化提取器
        
        Args:
            name: 提取器名称
            priority: 优先级，数值越大优先级越高
            enabled: 是否启用
        """
        self.name = name
        self.priority = priority
        self.enabled = enabled
        
        # 提取结果
        self.extracted_items: Set[str] = set()  # 提取到的原始项目
        self.filtered_items: Set[str] = set()   # 过滤后的项目
        self.mapping_results: Dict[str, str] = {}  # 映射结果 (原名 -> 新名)
        
        # 统计信息
        self.stats = {
            'total_extracted': 0,    # 总提取数量
            'total_filtered': 0,     # 过滤后数量
            'total_mapped': 0,       # 映射数量
            'ignored_count': 0,      # 忽略数量
            'processing_time': 0.0   # 处理耗时
        }
        
        # 日志器
        self.logger = Logger(f"Extractor.{name}")
        
        self.logger.debug(f"初始化提取器: {name} (优先级: {priority})")
    
    @abstractmethod
    def extract(self, content: str, file_path: Optional[str] = None) -> List[str]:
        """
        从内容中提取指定类型的标识符
        
        Args:
            content: 文件内容
            file_path: 文件路径 (可选，用于日志记录)
            
        Returns:
            提取到的标识符列表
            
        注意: 子类必须实现此方法，基于00001项目的提取逻辑
        """
        pass
    
    @abstractmethod
    def get_extraction_patterns(self) -> List[str]:
        """
        获取提取用的正则表达式模式
        
        Returns:
            正则表达式模式列表
            
        注意: 子类必须实现此方法，返回用于提取的正则表达式
        """
        pass
    
    def extract_from_file(self, file_path: str) -> List[str]:
        """
        从文件中提取标识符
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取到的标识符列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.extract(content, file_path)
        except Exception as e:
            self.logger.error(f"读取文件失败 {file_path}: {e}")
            return []
    
    def extract_from_files(self, file_paths: List[str]) -> List[str]:
        """
        从多个文件中提取标识符
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            提取到的标识符列表
        """
        all_items = []
        for file_path in file_paths:
            items = self.extract_from_file(file_path)
            all_items.extend(items)
        
        # 去重并返回
        return list(set(all_items))
    
    def filter_items(self, items: List[str], filter_func) -> List[str]:
        """
        过滤提取到的项目
        
        Args:
            items: 待过滤的项目列表
            filter_func: 过滤函数，接收项目名称，返回是否保留
            
        Returns:
            过滤后的项目列表
        """
        filtered = []
        for item in items:
            if filter_func(item, self.get_category()):
                filtered.append(item)
            else:
                self.stats['ignored_count'] += 1
                self.logger.debug(f"忽略项目: {item}")
        
        return filtered
    
    def generate_mappings(self, items: List[str], name_generator) -> Dict[str, str]:
        """
        为过滤后的项目生成新名称映射
        
        Args:
            items: 项目列表
            name_generator: 名称生成器
            
        Returns:
            映射字典 (原名 -> 新名)
        """
        mappings = {}
        
        for item in items:
            try:
                # 调用名称生成器生成新名称
                new_name = name_generator.generate_name(item, self.get_category())
                if new_name and new_name != item:
                    mappings[item] = new_name
                    self.logger.debug(f"生成映射: {item} -> {new_name}")
                else:
                    self.logger.debug(f"跳过映射: {item} (生成名称无效或相同)")
            except Exception as e:
                self.logger.error(f"生成名称失败 {item}: {e}")
        
        return mappings
    
    def process(self, content: str, filter_func, name_generator, 
               file_path: Optional[str] = None) -> Dict[str, str]:
        """
        完整的处理流程：提取 -> 过滤 -> 生成映射
        
        Args:
            content: 文件内容
            filter_func: 过滤函数
            name_generator: 名称生成器
            file_path: 文件路径 (可选)
            
        Returns:
            映射结果字典
        """
        import time
        start_time = time.time()
        
        try:
            # 1. 提取
            extracted = self.extract(content, file_path)
            self.extracted_items.update(extracted)
            self.stats['total_extracted'] = len(self.extracted_items)
            
            # 2. 过滤
            filtered = self.filter_items(extracted, filter_func)
            self.filtered_items.update(filtered)
            self.stats['total_filtered'] = len(self.filtered_items)
            
            # 3. 生成映射
            mappings = self.generate_mappings(filtered, name_generator)
            self.mapping_results.update(mappings)
            self.stats['total_mapped'] = len(self.mapping_results)
            
            # 记录处理时间
            self.stats['processing_time'] = time.time() - start_time
            
            self.logger.info(f"处理完成 - 提取: {len(extracted)}, 过滤: {len(filtered)}, 映射: {len(mappings)}")
            
            return mappings
            
        except Exception as e:
            self.logger.error(f"处理失败: {e}")
            return {}
    
    def get_category(self) -> str:
        """
        获取提取器类别名称
        
        Returns:
            类别名称 (如: properties, methods, classes等)
        """
        # 默认使用提取器名称，子类可以重写
        return self.name.lower().replace('_extractor', '')
    
    def get_file_extensions(self) -> List[str]:
        """
        获取支持的文件扩展名
        
        Returns:
            文件扩展名列表
        """
        # 默认支持Objective-C文件，子类可以重写
        return ['.h', '.m', '.mm']
    
    def should_process_file(self, file_path: str) -> bool:
        """
        判断是否应该处理指定文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否应该处理
        """
        if not self.enabled:
            return False
        
        file_ext = Path(file_path).suffix.lower()
        return file_ext in self.get_file_extensions()
    
    def reset_stats(self):
        """重置统计信息"""
        self.extracted_items.clear()
        self.filtered_items.clear()
        self.mapping_results.clear()
        
        self.stats = {
            'total_extracted': 0,
            'total_filtered': 0,
            'total_mapped': 0,
            'ignored_count': 0,
            'processing_time': 0.0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def get_mappings(self) -> Dict[str, str]:
        """获取映射结果"""
        return self.mapping_results.copy()
    
    def validate_extracted_item(self, item: str) -> bool:
        """
        验证提取到的项目是否有效
        
        Args:
            item: 提取到的项目
            
        Returns:
            是否有效
        """
        # 基础验证：不能为空，不能包含特殊字符等
        if not item or not item.strip():
            return False
        
        # 不能包含空格或特殊字符 (子类可以重写此方法)
        if ' ' in item or '\t' in item or '\n' in item:
            return False
        
        return True
    
    def __str__(self) -> str:
        """返回提取器的字符串表示"""
        status = "enabled" if self.enabled else "disabled"
        return f"{self.name}(priority={self.priority}, status={status})"
    
    def __repr__(self) -> str:
        """返回提取器的详细字符串表示"""
        return (f"{self.__class__.__name__}(name={self.name}, priority={self.priority}, "
                f"enabled={self.enabled}, extracted={len(self.extracted_items)})")
    
    def __lt__(self, other):
        """支持按优先级排序 (优先级高的排在前面)"""
        if isinstance(other, BaseExtractor):
            return self.priority > other.priority
        return NotImplemented
    
    def __eq__(self, other):
        """支持相等性比较"""
        if isinstance(other, BaseExtractor):
            return self.name == other.name
        return NotImplemented
    
    def __hash__(self):
        """支持哈希，可以用于集合和字典"""
        return hash(self.name)
