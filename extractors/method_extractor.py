"""
方法提取器

提取Objective-C中的实例方法和类方法名。
基于00001项目的n_method.py逻辑实现。

注意: 当前为占位符实现，具体提取逻辑需要基于00001项目的custom_core.py完善。
"""

import re
from typing import List, Optional

from .base_extractor import BaseExtractor


class MethodExtractor(BaseExtractor):
    """
    方法提取器类
    
    功能:
    - 提取Objective-C实例方法 (以-开头)
    - 提取Objective-C类方法 (以+开头)
    - 处理方法参数和返回类型
    - 支持多参数方法名的提取
    - 基于00001项目的提取逻辑实现
    
    TODO: 需要基于00001项目的custom_core.py中的方法提取逻辑完善实现
    """
    
    def __init__(self, name: str = "method", priority: int = 40, enabled: bool = True):
        """
        初始化方法提取器
        
        Args:
            name: 提取器名称
            priority: 优先级 (默认40，按架构文档中的优先级设置)
            enabled: 是否启用
        """
        super().__init__(name, priority, enabled)
        
        # 编译正则表达式以提高性能
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        # TODO: 这些是占位符模式，需要基于00001项目的custom_core.py中的正则表达式完善
        
        # 实例方法模式 (以-开头)
        self.instance_method_pattern = re.compile(
            r'-\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            re.MULTILINE
        )
        
        # 类方法模式 (以+开头)
        self.class_method_pattern = re.compile(
            r'\+\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            re.MULTILINE
        )
        
        # 多参数方法模式
        self.multi_param_method_pattern = re.compile(
            r'[-+]\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*(?:\s*:\s*\([^)]*\)\s*[A-Za-z_][A-Za-z0-9_]*\s*[A-Za-z_][A-Za-z0-9_]*)*)',
            re.MULTILINE
        )
    
    def get_extraction_patterns(self) -> List[str]:
        """
        获取提取用的正则表达式模式
        
        Returns:
            正则表达式模式列表
        """
        return [
            r'-\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'\+\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'[-+]\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*(?:\s*:\s*\([^)]*\)\s*[A-Za-z_][A-Za-z0-9_]*\s*[A-Za-z_][A-Za-z0-9_]*)*)'
        ]
    
    def extract(self, content: str, file_path: Optional[str] = None) -> List[str]:
        """
        从内容中提取方法名
        
        Args:
            content: 文件内容
            file_path: 文件路径 (可选)
            
        Returns:
            提取到的方法名列表
        """
        methods = []
        
        try:
            # TODO: 这是占位符实现，需要基于00001项目的custom_core.py中的方法提取逻辑完善
            
            # 1. 提取实例方法
            matches = self.instance_method_pattern.findall(content)
            for match in matches:
                method_name = match.strip()
                if self.validate_extracted_item(method_name):
                    methods.append(method_name)
            
            # 2. 提取类方法
            matches = self.class_method_pattern.findall(content)
            for match in matches:
                method_name = match.strip()
                if self.validate_extracted_item(method_name):
                    methods.append(method_name)
            
            # 3. 提取多参数方法 (需要特殊处理)
            matches = self.multi_param_method_pattern.findall(content)
            for match in matches:
                # 解析多参数方法名
                parsed_methods = self._parse_multi_param_method(match)
                for method_name in parsed_methods:
                    if self.validate_extracted_item(method_name):
                        methods.append(method_name)
            
            # 去重
            methods = list(set(methods))
            
            if file_path:
                self.logger.debug(f"从文件 {file_path} 提取到 {len(methods)} 个方法")
            
            return methods
            
        except Exception as e:
            self.logger.error(f"方法提取失败: {e}")
            if file_path:
                self.logger.error(f"文件: {file_path}")
            return []
    
    def _parse_multi_param_method(self, method_signature: str) -> List[str]:
        """
        解析多参数方法签名，提取方法名部分
        
        Args:
            method_signature: 方法签名
            
        Returns:
            解析出的方法名列表
        """
        # TODO: 这是占位符实现，需要基于00001项目的逻辑完善
        
        methods = []
        
        # 简单的解析逻辑，实际需要更复杂的处理
        parts = method_signature.split(':')
        if parts:
            first_part = parts[0].strip()
            if first_part and self.validate_extracted_item(first_part):
                methods.append(first_part)
        
        return methods
    
    def validate_extracted_item(self, item: str) -> bool:
        """
        验证提取到的方法名是否有效
        
        Args:
            item: 方法名
            
        Returns:
            是否有效
        """
        # 调用基类的基础验证
        if not super().validate_extracted_item(item):
            return False
        
        # 方法名特定验证
        # 1. 不能以数字开头
        if item and item[0].isdigit():
            return False
        
        # 2. 只能包含字母、数字和下划线
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', item):
            return False
        
        # 3. 不能是系统方法名 (这里只是示例，实际应该从配置中读取)
        system_methods = {
            'init', 'dealloc', 'alloc', 'new', 'copy', 'mutableCopy',
            'description', 'debugDescription', 'hash', 'isEqual',
            'performSelector', 'respondsToSelector', 'conformsToProtocol',
            'isKindOfClass', 'isMemberOfClass', 'viewDidLoad', 'viewWillAppear',
            'viewDidAppear', 'viewWillDisappear', 'viewDidDisappear'
        }
        
        if item in system_methods:
            return False
        
        # 4. 不能是init开头的方法 (根据配置决定是否保留)
        if item.startswith('init') and len(item) > 4:
            # 这里应该根据配置文件中的preserve_init_methods决定
            return False
        
        return True
    
    def get_category(self) -> str:
        """获取提取器类别名称"""
        return "methods"
    
    def get_file_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.h', '.m', '.mm']
    
    def extract_method_details(self, content: str) -> List[dict]:
        """
        提取方法的详细信息 (扩展功能)
        
        Args:
            content: 文件内容
            
        Returns:
            方法详细信息列表，每个元素包含方法名、参数、返回类型等
            
        TODO: 这是扩展功能，可以在基础提取完成后实现
        """
        # 占位符实现
        return []
    
    def is_instance_method(self, method_signature: str) -> bool:
        """
        判断是否为实例方法
        
        Args:
            method_signature: 方法签名
            
        Returns:
            是否为实例方法
        """
        return method_signature.strip().startswith('-')
    
    def is_class_method(self, method_signature: str) -> bool:
        """
        判断是否为类方法
        
        Args:
            method_signature: 方法签名
            
        Returns:
            是否为类方法
        """
        return method_signature.strip().startswith('+')
    
    def __str__(self) -> str:
        """返回提取器的字符串表示"""
        return f"MethodExtractor(priority={self.priority}, enabled={self.enabled})"


# 注意事项:
# 1. 当前实现是占位符，主要用于建立框架结构
# 2. 具体的提取逻辑需要基于00001项目的custom_core.py中的方法提取代码完善
# 3. 多参数方法的解析需要特别注意，要正确提取方法名的各个部分
# 4. 需要处理delegate方法的特殊情况，根据配置决定是否保留
# 5. init方法的处理需要根据preserve_init_methods配置决定
