# iMix iOS混淆工具 - Git忽略文件

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试和覆盖率
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 配置文件 (用户自定义)
*_config.yaml
*_config.yml
custom_config.*
user_config.*

# 输出文件
output/
iMix_output_*/
obfuscated_*/

# 测试相关
test_output/
test_projects/
*.test

# 文档生成
docs/_build/
docs/build/
site/

# 打包相关
*.tar.gz
*.zip
*.rar

# macOS相关
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows相关
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux相关
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 项目特定
# 混淆结果文件
obfuscation_mapping.json
被忽略的关键词日志.txt
工程中常量字符串列表.txt
工程中所有字符串列表.txt
project_framework_keywords.txt
project_all_string_words.txt
error_trace.log
process.log
obfuscation.log
obfuscation_statistics.json
obfuscation_report.txt

# 示例和测试项目
examples/*/output/
examples/*/iMix_output_*/
test_data/*/output/

# 备份文件
*.bak
*.backup
backup_*/

# 缓存文件
.cache/
cache/

# 本地开发文件
local_*
dev_*
debug_*

# 依赖冻结文件 (可选)
requirements_frozen.txt

# 用户自定义词库
custom_dictionary.json
user_dictionary.json

# 临时配置
temp_config.*
debug_config.*
