#!/usr/bin/env python3
"""
iMix iOS混淆工具 - 安装配置文件

基于00001项目的核心逻辑，创建一个通用的iOS代码混淆工具。
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding='utf-8') if readme_file.exists() else ""

# 读取requirements.txt
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                # 移除注释部分
                if '#' in line:
                    line = line.split('#')[0].strip()
                if line:
                    requirements.append(line)

# 过滤出核心依赖 (排除开发和打包依赖)
core_requirements = [req for req in requirements if not any(
    dev_pkg in req for dev_pkg in ['pytest', 'black', 'flake8', 'mypy', 'pyinstaller', 'setuptools', 'wheel']
)]

setup(
    # 基本信息
    name="imix",
    version="1.0.0",
    description="通用iOS代码混淆工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    
    # 作者信息
    author="iMix Team",
    author_email="",
    
    # 项目链接
    url="",
    project_urls={
        "Source": "",
        "Bug Reports": "",
        "Documentation": "",
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Build Tools",
        "Topic :: Security :: Cryptography",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.13",
        "Programming Language :: Objective C",
        "Operating System :: OS Independent",
        "Environment :: Console",
    ],
    
    # Python版本要求
    python_requires=">=3.13.3",
    
    # 包配置
    packages=find_packages(exclude=["tests", "tests.*"]),
    include_package_data=True,
    
    # 依赖包
    install_requires=core_requirements,
    
    # 额外依赖组
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-cov>=4.0",
            "black>=23.0",
            "flake8>=6.0",
            "mypy>=1.0",
        ],
        "build": [
            "pyinstaller>=5.0",
            "setuptools>=68.0",
            "wheel>=0.40",
        ],
    },
    
    # 包数据
    package_data={
        "config": [
            "*.yaml",
            "keywords/*.txt",
            "keywords/*.json",
        ],
    },
    
    # 命令行入口点
    entry_points={
        "console_scripts": [
            "imix=cli.cli_main:main",
        ],
    },
    
    # 关键词
    keywords=[
        "ios", "obfuscation", "code-protection", "objective-c", 
        "swift", "xcode", "security", "reverse-engineering"
    ],
    
    # 许可证
    license="MIT",
    
    # 项目状态
    zip_safe=False,
)

# 安装后的说明信息
print("""
iMix iOS混淆工具安装完成!

使用方法:
  imix obfuscate /path/to/ios/project          # 混淆iOS项目
  imix validate /path/to/ios/project           # 验证项目结构
  imix config show                             # 显示当前配置
  imix --help                                  # 查看帮助信息

更多信息请参考README.md文件。
""")
