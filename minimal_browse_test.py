#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化文件浏览测试
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import traceback

class MinimalBrowseTest:
    def __init__(self, root):
        self.root = root
        self.root.title("最小化浏览测试")
        self.root.geometry("600x400")
        
        print("初始化最小化测试...")
        
        # 初始化变量
        self.test_path = tk.StringVar()
        
        # 创建界面
        self.create_ui()
        print("界面创建完成")
    
    def create_ui(self):
        """创建用户界面"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="最小化浏览测试", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_label = ttk.Label(main_frame, 
                              text="测试各种文件/文件夹浏览功能，按按钮后查看终端输出")
        info_label.pack(pady=(0, 20))
        
        # 路径显示
        path_group = ttk.LabelFrame(main_frame, text="选择的路径", padding="15")
        path_group.pack(fill=tk.X, pady=(0, 20))
        
        self.path_entry = ttk.Entry(path_group, textvariable=self.test_path, width=60)
        self.path_entry.pack(fill=tk.X)
        
        # 测试按钮组
        button_group = ttk.LabelFrame(main_frame, text="测试按钮", padding="15")
        button_group.pack(fill=tk.X)
        
        # 按钮网格
        button_frame = ttk.Frame(button_group)
        button_frame.pack(fill=tk.X)
        
        # 第一行按钮
        row1_frame = ttk.Frame(button_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(row1_frame, text="1. 选择文件夹", 
                  command=self.test_askdirectory).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row1_frame, text="2. 选择任意文件", 
                  command=self.test_askopenfilename).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row1_frame, text="3. 选择.pbxproj文件", 
                  command=self.test_askopen_pbxproj).pack(side=tk.LEFT)
        
        # 第二行按钮
        row2_frame = ttk.Frame(button_frame)
        row2_frame.pack(fill=tk.X)
        
        ttk.Button(row2_frame, text="4. 保存文件对话框", 
                  command=self.test_asksaveasfilename).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row2_frame, text="5. 显示路径", 
                  command=self.show_current_path).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(row2_frame, text="6. 清空路径", 
                  command=self.clear_path).pack(side=tk.LEFT)
        
        # 状态显示
        status_group = ttk.LabelFrame(main_frame, text="状态", padding="15")
        status_group.pack(fill=tk.X, pady=(20, 0))
        
        self.status_label = ttk.Label(status_group, text="就绪")
        self.status_label.pack()
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
        print(f"状态: {message}")
    
    def test_askdirectory(self):
        """测试选择文件夹"""
        print("\n=== 测试1: 选择文件夹 ===")
        self.update_status("正在打开文件夹选择对话框...")
        try:
            path = filedialog.askdirectory(
                title="选择文件夹",
                initialdir=os.path.expanduser("~"),
                mustexist=True
            )
            if path:
                self.test_path.set(path)
                self.update_status(f"成功选择文件夹: {os.path.basename(path)}")
                print(f"✅ 选择了文件夹: {path}")
            else:
                self.update_status("用户取消了选择")
                print("⚠️ 用户取消了选择")
        except Exception as e:
            error_msg = f"选择文件夹失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
    
    def test_askopenfilename(self):
        """测试选择任意文件"""
        print("\n=== 测试2: 选择任意文件 ===")
        self.update_status("正在打开文件选择对话框...")
        try:
            path = filedialog.askopenfilename(
                title="选择文件",
                initialdir=os.path.expanduser("~"),
                filetypes=[("所有文件", "*.*")]
            )
            if path:
                self.test_path.set(path)
                self.update_status(f"成功选择文件: {os.path.basename(path)}")
                print(f"✅ 选择了文件: {path}")
            else:
                self.update_status("用户取消了选择")
                print("⚠️ 用户取消了选择")
        except Exception as e:
            error_msg = f"选择文件失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
    
    def test_askopen_pbxproj(self):
        """测试选择.pbxproj文件"""
        print("\n=== 测试3: 选择.pbxproj文件 ===")
        self.update_status("正在打开pbxproj文件选择对话框...")
        try:
            # 修复文件类型设置，避免nil对象错误
            filetypes = [
                ("所有文件", "*.*"),
                ("pbxproj文件", "*.pbxproj")
            ]
            path = filedialog.askopenfilename(
                title="选择project.pbxproj文件",
                initialdir=os.path.expanduser("~"),
                filetypes=filetypes
            )
            if path:
                self.test_path.set(path)
                self.update_status(f"成功选择pbxproj文件: {os.path.basename(path)}")
                print(f"✅ 选择了pbxproj文件: {path}")
            else:
                self.update_status("用户取消了选择")
                print("⚠️ 用户取消了选择")
        except Exception as e:
            error_msg = f"选择pbxproj文件失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
    
    def test_asksaveasfilename(self):
        """测试保存文件对话框"""
        print("\n=== 测试4: 保存文件对话框 ===")
        self.update_status("正在打开保存文件对话框...")
        try:
            path = filedialog.asksaveasfilename(
                title="保存文件",
                initialdir=os.path.expanduser("~"),
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if path:
                self.test_path.set(path)
                self.update_status(f"选择保存路径: {os.path.basename(path)}")
                print(f"✅ 选择保存路径: {path}")
            else:
                self.update_status("用户取消了选择")
                print("⚠️ 用户取消了选择")
        except Exception as e:
            error_msg = f"保存文件对话框失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
    
    def show_current_path(self):
        """显示当前路径"""
        print("\n=== 测试5: 显示当前路径 ===")
        try:
            path = self.test_path.get()
            if path:
                message = f"当前路径: {path}"
                self.update_status(message)
                print(f"📋 {message}")
                messagebox.showinfo("当前路径", message)
            else:
                message = "当前未选择任何路径"
                self.update_status(message)
                print(f"⚠️ {message}")
                messagebox.showwarning("提示", message)
        except Exception as e:
            error_msg = f"显示路径失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
    
    def clear_path(self):
        """清空路径"""
        print("\n=== 测试6: 清空路径 ===")
        try:
            self.test_path.set("")
            self.update_status("路径已清空")
            print("🧹 路径已清空")
        except Exception as e:
            error_msg = f"清空路径失败: {e}"
            self.update_status(error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()

def main():
    """主函数"""
    print("启动最小化浏览测试...")
    try:
        root = tk.Tk()
        app = MinimalBrowseTest(root)
        print("测试界面启动成功，请点击按钮进行测试")
        print("观察终端输出和界面状态")
        root.mainloop()
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 