"""
名称生成器

生成混淆后的名称，基于00001项目的oc_tool.py中的随机名称生成逻辑实现。
基于ARCHITECTURE.md中的名称生成器设计。
"""

import json
import random
import string
from typing import Dict, List, Set, Optional, Any
from pathlib import Path

from ..config import ConfigManager
from ..logging import Logger


class NameGenerator:
    """
    名称生成器类
    
    功能:
    - 生成混淆后的名称
    - 基于词库生成有意义的名称
    - 确保名称唯一性
    - 保持命名规范 (首字母大小写等)
    - 完全复用00001项目的random_one_string逻辑
    
    生成策略:
    - 基于原名称长度的随机生成
    - 保持首字母大小写规则
    - 特殊前缀保留(如init方法)
    - 后缀关键词保留
    - 全局唯一性检查
    """
    
    def __init__(self, config: Optional[ConfigManager] = None):
        """
        初始化名称生成器
        
        Args:
            config: 配置管理器，如果为None则使用默认配置
        """
        self.config = config
        self.logger = Logger("NameGenerator")
        
        # 词库数据
        self.word_dict: Dict[str, List[str]] = {}  # 按长度分类的词库
        self.describe_words: Dict[str, List[str]] = {}  # 描述词配置
        
        # 已生成的名称集合 (确保唯一性)
        self.generated_names: Set[str] = set()
        
        # 生成统计
        self.generation_stats = {
            'total_generated': 0,
            'unique_names': 0,
            'collision_count': 0,
            'fallback_count': 0
        }
        
        # 加载词库
        self._load_word_dictionary()
        
        self.logger.info("名称生成器初始化完成")
    
    def _load_word_dictionary(self):
        """加载词库文件"""
        try:
            if self.config:
                word_config = self.config.get('word_dictionary', {})
                main_dict = word_config.get('main_dictionary', 'word_dictionary.json')
                
                # 构建词库文件路径
                config_dir = Path(self.config.default_config_path).parent
                keywords_dir = config_dir / "keywords"
                dict_file = keywords_dir / main_dict
            else:
                # 使用默认路径
                current_dir = Path(__file__).parent.parent
                dict_file = current_dir / "config" / "keywords" / "word_dictionary.json"
            
            if dict_file.exists():
                with open(dict_file, 'r', encoding='utf-8') as f:
                    dict_data = json.load(f)
                    self.word_dict = dict_data.get('word_dict', {})
                    self.describe_words = dict_data.get('describe_words', {})
                
                self.logger.info(f"加载词库完成 - 长度类别: {len(self.word_dict)}, "
                               f"描述词类别: {len(self.describe_words)}")
            else:
                self.logger.warning(f"词库文件不存在: {dict_file}")
                self._create_default_word_dict()
                
        except Exception as e:
            self.logger.error(f"加载词库失败: {e}")
            self._create_default_word_dict()
    
    def _create_default_word_dict(self):
        """创建默认词库"""
        self.word_dict = {
            "3": ["app", "api", "url", "web", "net", "tcp", "udp", "xml", "sql", "css"],
            "4": ["data", "info", "user", "name", "text", "file", "path", "code", "type", "size"],
            "5": ["model", "value", "index", "count", "total", "start", "begin", "final", "image"],
            "6": ["object", "string", "number", "double", "float", "array", "vector", "matrix"],
            "7": ["manager", "handler", "service", "factory", "builder", "creator", "adapter"],
            "8": ["instance", "template", "document", "resource", "database", "network"]
        }
        
        self.describe_words = {
            "property": ["attr", "prop", "field", "member", "var"],
            "method": ["func", "proc", "action", "operation", "task"],
            "class": ["obj", "entity", "model", "type", "kind"],
            "variable": ["val", "data", "info", "item", "element"]
        }
        
        self.logger.info("使用默认词库")
    
    def generate_name(self, original: str, category: str = "") -> str:
        """
        生成混淆后的名称
        
        Args:
            original: 原始名称
            category: 类别 (如: properties, methods, classes等)
            
        Returns:
            生成的新名称
            
        注意: 这是核心生成逻辑，基于00001项目的random_one_string函数实现
        """
        self.generation_stats['total_generated'] += 1
        
        try:
            # TODO: 这是占位符实现，需要基于00001项目的oc_tool.py中的random_one_string逻辑完善
            
            # 1. 获取原名称的基本信息
            original_length = len(original)
            is_uppercase_first = original and original[0].isupper()
            
            # 2. 生成新名称
            new_name = self._generate_random_name(original_length, category)
            
            # 3. 保持首字母大小写规则
            if new_name:
                if is_uppercase_first:
                    new_name = new_name[0].upper() + new_name[1:] if len(new_name) > 1 else new_name.upper()
                else:
                    new_name = new_name[0].lower() + new_name[1:] if len(new_name) > 1 else new_name.lower()
            
            # 4. 确保唯一性
            new_name = self._ensure_uniqueness(new_name)
            
            # 5. 记录生成的名称
            if new_name:
                self.generated_names.add(new_name)
                self.generation_stats['unique_names'] += 1
                self.logger.debug(f"生成名称: {original} -> {new_name}")
            
            return new_name
            
        except Exception as e:
            self.logger.error(f"生成名称失败 {original}: {e}")
            return self._generate_fallback_name(original)
    
    def _generate_random_name(self, target_length: int, category: str = "") -> str:
        """
        生成指定长度的随机名称
        
        Args:
            target_length: 目标长度
            category: 类别
            
        Returns:
            生成的名称
        """
        # TODO: 这是占位符实现，需要基于00001项目的逻辑完善
        
        # 1. 尝试从词库中获取合适长度的词汇
        length_key = str(target_length)
        if length_key in self.word_dict and self.word_dict[length_key]:
            words = self.word_dict[length_key]
            base_word = random.choice(words)
        else:
            # 2. 如果没有合适长度的词汇，选择最接近的长度
            available_lengths = [int(k) for k in self.word_dict.keys() if self.word_dict[k]]
            if available_lengths:
                closest_length = min(available_lengths, key=lambda x: abs(x - target_length))
                words = self.word_dict[str(closest_length)]
                base_word = random.choice(words)
            else:
                # 3. 如果词库为空，生成随机字符串
                return self._generate_random_string(target_length)
        
        # 4. 根据类别添加描述词前缀
        if category in self.describe_words:
            describe_word = random.choice(self.describe_words[category])
            # 组合描述词和基础词
            combined = describe_word + base_word.capitalize()
            if len(combined) <= target_length + 3:  # 允许一定的长度偏差
                return combined
        
        return base_word
    
    def _generate_random_string(self, length: int) -> str:
        """
        生成纯随机字符串 (后备方案)
        
        Args:
            length: 字符串长度
            
        Returns:
            随机字符串
        """
        if length <= 0:
            length = 5  # 默认长度
        
        # 第一个字符必须是字母
        first_char = random.choice(string.ascii_letters)
        
        # 其余字符可以是字母或数字
        remaining_chars = ''.join(random.choices(
            string.ascii_letters + string.digits, 
            k=length-1
        ))
        
        return first_char + remaining_chars
    
    def _ensure_uniqueness(self, name: str) -> str:
        """
        确保名称唯一性
        
        Args:
            name: 候选名称
            
        Returns:
            唯一的名称
        """
        if not name:
            return self._generate_fallback_name("unknown")
        
        original_name = name
        counter = 1
        
        # 如果名称已存在，添加数字后缀
        while name in self.generated_names:
            self.generation_stats['collision_count'] += 1
            name = f"{original_name}{counter}"
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                self.logger.warning(f"名称冲突过多，使用随机后缀: {original_name}")
                random_suffix = random.randint(1000, 9999)
                name = f"{original_name}{random_suffix}"
                break
        
        return name
    
    def _generate_fallback_name(self, original: str) -> str:
        """
        生成后备名称 (当其他方法失败时使用)
        
        Args:
            original: 原始名称
            
        Returns:
            后备名称
        """
        self.generation_stats['fallback_count'] += 1
        
        # 使用简单的随机字符串
        fallback = self._generate_random_string(max(len(original), 5))
        fallback = self._ensure_uniqueness(fallback)
        
        self.logger.debug(f"使用后备名称: {original} -> {fallback}")
        return fallback
    
    def batch_generate(self, names: List[str], category: str = "") -> Dict[str, str]:
        """
        批量生成名称
        
        Args:
            names: 原始名称列表
            category: 类别
            
        Returns:
            名称映射字典 {original: new_name}
        """
        mappings = {}
        for name in names:
            new_name = self.generate_name(name, category)
            if new_name and new_name != name:
                mappings[name] = new_name
        
        return mappings
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        stats = self.generation_stats.copy()
        
        # 计算成功率
        total = stats['total_generated']
        if total > 0:
            stats['success_rate'] = (stats['unique_names'] / total) * 100
            stats['collision_rate'] = (stats['collision_count'] / total) * 100
            stats['fallback_rate'] = (stats['fallback_count'] / total) * 100
        
        stats['total_unique_names'] = len(self.generated_names)
        
        return stats
    
    def reset_stats(self):
        """重置生成统计信息"""
        self.generation_stats = {
            'total_generated': 0,
            'unique_names': 0,
            'collision_count': 0,
            'fallback_count': 0
        }
        self.logger.info("重置生成统计信息")
    
    def clear_generated_names(self):
        """清空已生成名称集合"""
        self.generated_names.clear()
        self.logger.info("清空已生成名称集合")
    
    def add_custom_words(self, length: int, words: List[str]):
        """
        添加自定义词汇到词库
        
        Args:
            length: 词汇长度
            words: 词汇列表
        """
        length_key = str(length)
        if length_key not in self.word_dict:
            self.word_dict[length_key] = []
        
        self.word_dict[length_key].extend(words)
        self.logger.info(f"添加自定义词汇: 长度{length}, {len(words)}个词汇")
    
    def __str__(self) -> str:
        """返回生成器的字符串表示"""
        return f"NameGenerator(generated={len(self.generated_names)})"
    
    def __repr__(self) -> str:
        """返回生成器的详细字符串表示"""
        stats = self.get_generation_stats()
        return f"NameGenerator(stats={stats})"


# 创建默认的名称生成器实例
default_name_generator = NameGenerator()
