"""
提取器注册系统

管理所有提取器的注册、配置和执行顺序。
基于ARCHITECTURE.md中的提取器注册系统设计实现。
"""

from typing import Dict, List, Type, Optional, Any
from ..extractors.base_extractor import BaseExtractor
from ..logging import Logger


class ExtractorRegistry:
    """
    提取器注册中心
    
    功能:
    - 管理所有提取器的注册和配置
    - 控制提取器的执行顺序
    - 提供提取器的生命周期管理
    - 支持动态启用/禁用提取器
    """
    
    def __init__(self):
        """初始化提取器注册中心"""
        self.extractors: Dict[str, BaseExtractor] = {}  # 已注册的提取器实例
        self.extractor_classes: Dict[str, Type[BaseExtractor]] = {}  # 提取器类
        self.execution_order: List[str] = []  # 执行顺序
        
        # 日志器
        self.logger = Logger("ExtractorRegistry")
        
        self.logger.info("提取器注册中心初始化完成")
    
    def register_class(self, name: str, extractor_class: Type[BaseExtractor], 
                      priority: int = 0, enabled: bool = True):
        """
        注册提取器类
        
        Args:
            name: 提取器名称
            extractor_class: 提取器类
            priority: 优先级
            enabled: 是否启用
        """
        if not issubclass(extractor_class, BaseExtractor):
            raise ValueError(f"提取器类必须继承自BaseExtractor: {extractor_class}")
        
        self.extractor_classes[name] = extractor_class
        
        # 创建实例
        extractor_instance = extractor_class(name, priority, enabled)
        self.extractors[name] = extractor_instance
        
        self.logger.info(f"注册提取器类: {name} (优先级: {priority})")
        
        # 更新执行顺序
        self._update_execution_order()
    
    def register_instance(self, extractor: BaseExtractor):
        """
        注册提取器实例
        
        Args:
            extractor: 提取器实例
        """
        if not isinstance(extractor, BaseExtractor):
            raise ValueError(f"必须是BaseExtractor的实例: {extractor}")
        
        name = extractor.name
        self.extractors[name] = extractor
        self.extractor_classes[name] = extractor.__class__
        
        self.logger.info(f"注册提取器实例: {name}")
        
        # 更新执行顺序
        self._update_execution_order()
    
    def unregister(self, name: str):
        """
        注销提取器
        
        Args:
            name: 提取器名称
        """
        if name in self.extractors:
            del self.extractors[name]
            self.logger.info(f"注销提取器: {name}")
        
        if name in self.extractor_classes:
            del self.extractor_classes[name]
        
        # 更新执行顺序
        self._update_execution_order()
    
    def get_extractor(self, name: str) -> Optional[BaseExtractor]:
        """
        获取提取器实例
        
        Args:
            name: 提取器名称
            
        Returns:
            提取器实例，如果不存在则返回None
        """
        return self.extractors.get(name)
    
    def get_all_extractors(self) -> Dict[str, BaseExtractor]:
        """获取所有提取器实例"""
        return self.extractors.copy()
    
    def get_enabled_extractors(self) -> Dict[str, BaseExtractor]:
        """获取所有启用的提取器"""
        return {name: extractor for name, extractor in self.extractors.items() 
                if extractor.enabled}
    
    def get_ordered_extractors(self) -> List[BaseExtractor]:
        """
        按优先级返回提取器列表
        
        Returns:
            按优先级排序的提取器列表 (优先级高的在前)
        """
        enabled_extractors = [self.extractors[name] for name in self.execution_order 
                            if name in self.extractors and self.extractors[name].enabled]
        return enabled_extractors
    
    def _update_execution_order(self):
        """更新执行顺序"""
        # 按优先级排序 (优先级高的在前)
        sorted_extractors = sorted(self.extractors.values(), 
                                 key=lambda x: x.priority, reverse=True)
        self.execution_order = [extractor.name for extractor in sorted_extractors]
        
        self.logger.debug(f"更新执行顺序: {self.execution_order}")
    
    def enable_extractor(self, name: str):
        """
        启用提取器
        
        Args:
            name: 提取器名称
        """
        if name in self.extractors:
            self.extractors[name].enabled = True
            self.logger.info(f"启用提取器: {name}")
        else:
            self.logger.warning(f"提取器不存在: {name}")
    
    def disable_extractor(self, name: str):
        """
        禁用提取器
        
        Args:
            name: 提取器名称
        """
        if name in self.extractors:
            self.extractors[name].enabled = False
            self.logger.info(f"禁用提取器: {name}")
        else:
            self.logger.warning(f"提取器不存在: {name}")
    
    def set_extractor_priority(self, name: str, priority: int):
        """
        设置提取器优先级
        
        Args:
            name: 提取器名称
            priority: 新的优先级
        """
        if name in self.extractors:
            old_priority = self.extractors[name].priority
            self.extractors[name].priority = priority
            self.logger.info(f"设置提取器优先级: {name} ({old_priority} -> {priority})")
            
            # 更新执行顺序
            self._update_execution_order()
        else:
            self.logger.warning(f"提取器不存在: {name}")
    
    def configure_from_dict(self, config: Dict[str, Any]):
        """
        从配置字典配置提取器
        
        Args:
            config: 配置字典，包含enabled和priorities等配置
        """
        # 配置启用状态
        enabled_extractors = config.get('enabled', [])
        for name in self.extractors:
            if name in enabled_extractors:
                self.enable_extractor(name)
            else:
                self.disable_extractor(name)
        
        # 配置优先级
        priorities = config.get('priorities', {})
        for name, priority in priorities.items():
            # 移除_extractor后缀来匹配提取器名称
            extractor_name = name.replace('_extractor', '')
            if extractor_name in self.extractors:
                self.set_extractor_priority(extractor_name, priority)
        
        self.logger.info("从配置字典配置提取器完成")
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """
        获取注册中心统计信息
        
        Returns:
            统计信息字典
        """
        total_extractors = len(self.extractors)
        enabled_extractors = len(self.get_enabled_extractors())
        disabled_extractors = total_extractors - enabled_extractors
        
        return {
            'total_extractors': total_extractors,
            'enabled_extractors': enabled_extractors,
            'disabled_extractors': disabled_extractors,
            'execution_order': self.execution_order.copy(),
            'extractor_details': {
                name: {
                    'priority': extractor.priority,
                    'enabled': extractor.enabled,
                    'category': extractor.get_category(),
                    'file_extensions': extractor.get_file_extensions()
                }
                for name, extractor in self.extractors.items()
            }
        }
    
    def validate_registry(self) -> bool:
        """
        验证注册中心的有效性
        
        Returns:
            是否有效
        """
        try:
            # 检查是否有启用的提取器
            enabled_extractors = self.get_enabled_extractors()
            if not enabled_extractors:
                self.logger.warning("没有启用的提取器")
                return False
            
            # 检查提取器实例的有效性
            for name, extractor in self.extractors.items():
                if not isinstance(extractor, BaseExtractor):
                    self.logger.error(f"无效的提取器实例: {name}")
                    return False
            
            self.logger.info("注册中心验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"注册中心验证失败: {e}")
            return False
    
    def reset_all_stats(self):
        """重置所有提取器的统计信息"""
        for extractor in self.extractors.values():
            extractor.reset_stats()
        self.logger.info("重置所有提取器统计信息")
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有提取器的统计信息"""
        return {name: extractor.get_stats() for name, extractor in self.extractors.items()}
    
    def get_all_mappings(self) -> Dict[str, Dict[str, str]]:
        """获取所有提取器的映射结果"""
        return {name: extractor.get_mappings() for name, extractor in self.extractors.items()}
    
    def __len__(self) -> int:
        """返回注册的提取器数量"""
        return len(self.extractors)
    
    def __contains__(self, name: str) -> bool:
        """检查是否包含指定名称的提取器"""
        return name in self.extractors
    
    def __iter__(self):
        """支持迭代，按执行顺序返回提取器"""
        for name in self.execution_order:
            if name in self.extractors:
                yield self.extractors[name]
    
    def __str__(self) -> str:
        """返回注册中心的字符串表示"""
        enabled_count = len(self.get_enabled_extractors())
        total_count = len(self.extractors)
        return f"ExtractorRegistry(total={total_count}, enabled={enabled_count})"
    
    def __repr__(self) -> str:
        """返回注册中心的详细字符串表示"""
        return (f"ExtractorRegistry(extractors={list(self.extractors.keys())}, "
                f"order={self.execution_order})")


# 创建默认的全局提取器注册中心
default_registry = ExtractorRegistry()
