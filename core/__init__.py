"""
核心处理模块

包含混淆处理的核心逻辑，包括:
- ObfuscationProcessor: 主要的混淆处理器，协调各个模块的工作
- FilterSystem: 过滤器系统，判断哪些标识符需要混淆
- NameGenerator: 名称生成器，生成混淆后的名称
- ObfuscationMapping: 混淆映射管理器，维护原名称和混淆名称的对应关系
"""

# 导入核心类
from .extractor import ExtractorRegistry, default_registry
from .filter import FilterSystem, default_filter_system
from .generator import NameGenerator, default_name_generator
from .processor import ObfuscationProcessor, default_processor

# TODO: 其他核心类待实现
# from .mapping_manager import ObfuscationMapping

__all__ = [
    "ExtractorRegistry",
    "default_registry",
    "FilterSystem",
    "default_filter_system",
    "NameGenerator",
    "default_name_generator",
    "ObfuscationProcessor",
    "default_processor",
    # TODO: 其他核心类待实现
    # "ObfuscationMapping"
]
