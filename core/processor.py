"""
混淆处理器

主要的混淆处理器，协调各个模块的工作。
基于ARCHITECTURE.md中的核心处理器设计实现。
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from ..config import ConfigManager
from ..logging import ObfuscationLogger, ProgressLogger, Logger
from .extractor import ExtractorRegistry
from .filter import FilterSystem
from .generator import NameGenerator
from ..extractors import PropertyExtractor, MethodExtractor


class ObfuscationProcessor:
    """
    混淆处理器类
    
    功能:
    - 协调各个模块的工作
    - 管理完整的混淆流程
    - 提供进度跟踪和错误处理
    - 生成详细的处理报告
    
    处理流程:
    1. 项目拷贝与工程检测
    2. 模块初始化
    3. 项目关键词预提取
    4. 各类型关键词提取
    5. 过滤与验证
    6. 名称生成
    7. 代码替换与输出
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化混淆处理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        # 初始化配置
        self.config = ConfigManager(config_path)
        
        # 初始化日志系统
        self.logger = Logger("ObfuscationProcessor")
        self.progress_logger = None  # 在process_project中初始化
        self.obfuscation_logger = None  # 在process_project中初始化
        
        # 初始化核心模块
        self.extractor_registry = ExtractorRegistry()
        self.filter_system = FilterSystem(self.config)
        self.name_generator = NameGenerator(self.config)
        
        # 处理状态
        self.is_processing = False
        self.current_project_path = None
        self.output_directory = None
        
        # 处理结果
        self.processing_results = {}
        
        # 注册默认提取器
        self._register_default_extractors()
        
        self.logger.info("混淆处理器初始化完成")
    
    def _register_default_extractors(self):
        """注册默认的提取器"""
        try:
            # 从配置中获取提取器配置
            extractor_config = self.config.get_extractor_config()
            
            # 注册已实现的提取器
            self.extractor_registry.register_class(
                "property", PropertyExtractor, 
                priority=extractor_config.get('priorities', {}).get('property_extractor', 100)
            )
            
            self.extractor_registry.register_class(
                "method", MethodExtractor,
                priority=extractor_config.get('priorities', {}).get('method_extractor', 40)
            )
            
            # TODO: 注册其他提取器 (待实现)
            
            # 根据配置启用/禁用提取器
            self.extractor_registry.configure_from_dict(extractor_config)
            
            self.logger.info("默认提取器注册完成")
            
        except Exception as e:
            self.logger.error(f"注册默认提取器失败: {e}")
    
    def process_project(self, project_path: str, output_path: Optional[str] = None) -> bool:
        """
        处理整个项目的混淆
        
        Args:
            project_path: 项目路径
            output_path: 输出路径，如果为None则在项目目录下创建
            
        Returns:
            处理是否成功
        """
        if self.is_processing:
            self.logger.error("已有项目正在处理中")
            return False
        
        self.is_processing = True
        self.current_project_path = project_path
        
        try:
            # 1. 验证项目路径
            if not self._validate_project_path(project_path):
                return False
            
            # 2. 创建输出目录
            self.output_directory = self._create_output_directory(project_path, output_path)
            if not self.output_directory:
                return False
            
            # 3. 初始化日志系统
            self._initialize_logging()
            
            # 4. 开始处理流程
            self.progress_logger.start_process("iOS项目混淆", total_steps=7)
            
            # 步骤1: 项目拷贝与工程检测
            if not self._step1_copy_and_detect_project():
                return False
            
            # 步骤2: 模块初始化
            if not self._step2_initialize_modules():
                return False
            
            # 步骤3: 项目关键词预提取
            if not self._step3_extract_project_keywords():
                return False
            
            # 步骤4: 各类型关键词提取
            if not self._step4_extract_code_elements():
                return False
            
            # 步骤5: 过滤与验证
            if not self._step5_filter_and_validate():
                return False
            
            # 步骤6: 名称生成
            if not self._step6_generate_names():
                return False
            
            # 步骤7: 代码替换与输出
            if not self._step7_replace_and_output():
                return False
            
            # 完成处理
            self._finalize_processing()
            
            self.progress_logger.complete_process("iOS项目混淆", self.processing_results)
            self.logger.info(f"项目混淆完成: {project_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"项目处理失败: {e}")
            self.progress_logger.log_error(f"项目处理失败: {e}")
            return False
            
        finally:
            self.is_processing = False
    
    def _validate_project_path(self, project_path: str) -> bool:
        """验证项目路径"""
        if not os.path.exists(project_path):
            self.logger.error(f"项目路径不存在: {project_path}")
            return False
        
        if not os.path.isdir(project_path):
            self.logger.error(f"项目路径不是目录: {project_path}")
            return False
        
        # 检查是否包含.xcodeproj文件
        xcodeproj_files = list(Path(project_path).glob("*.xcodeproj"))
        if not xcodeproj_files:
            self.logger.error(f"项目目录中未找到.xcodeproj文件: {project_path}")
            return False
        
        self.logger.info(f"项目验证通过: {project_path}")
        return True
    
    def _create_output_directory(self, project_path: str, output_path: Optional[str]) -> Optional[str]:
        """创建输出目录"""
        try:
            if output_path:
                output_dir = output_path
            else:
                # 在项目目录下创建时间戳命名的输出目录
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                output_dir = os.path.join(project_path, f"iMix_output_{timestamp}")
            
            os.makedirs(output_dir, exist_ok=True)
            self.logger.info(f"创建输出目录: {output_dir}")
            
            return output_dir
            
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            return None
    
    def _initialize_logging(self):
        """初始化日志系统"""
        # 创建进度日志器
        self.progress_logger = ProgressLogger(self.logger)
        
        # 创建混淆日志器
        log_config = self.config.get_output_config()
        self.obfuscation_logger = ObfuscationLogger(self.output_directory, log_config)
    
    def _step1_copy_and_detect_project(self) -> bool:
        """步骤1: 项目拷贝与工程检测"""
        self.progress_logger.start_step("项目拷贝与工程检测")
        
        try:
            # TODO: 实现项目拷贝逻辑
            # 1. 创建混淆工作目录
            # 2. 完整拷贝原项目到工作目录
            # 3. 查找并确定.xcodeproj文件
            
            self.progress_logger.update_substep("检测.xcodeproj文件")
            xcodeproj_files = list(Path(self.current_project_path).glob("*.xcodeproj"))
            
            if xcodeproj_files:
                self.processing_results['xcodeproj_file'] = str(xcodeproj_files[0])
                self.progress_logger.update_substep(f"找到工程文件: {xcodeproj_files[0].name}")
            
            self.progress_logger.complete_step("项目拷贝与工程检测", len(xcodeproj_files))
            return True
            
        except Exception as e:
            self.progress_logger.log_error(f"项目拷贝失败: {e}")
            return False
    
    def _step2_initialize_modules(self) -> bool:
        """步骤2: 模块初始化"""
        self.progress_logger.start_step("模块初始化")
        
        try:
            # 验证各模块状态
            if not self.extractor_registry.validate_registry():
                self.progress_logger.log_error("提取器注册中心验证失败")
                return False
            
            # 重置统计信息
            self.extractor_registry.reset_all_stats()
            self.filter_system.reset_stats()
            self.name_generator.reset_stats()
            
            self.progress_logger.complete_step("模块初始化")
            return True
            
        except Exception as e:
            self.progress_logger.log_error(f"模块初始化失败: {e}")
            return False
    
    def _step3_extract_project_keywords(self) -> bool:
        """步骤3: 项目关键词预提取"""
        self.progress_logger.start_step("项目关键词预提取")
        
        try:
            # TODO: 实现项目关键词提取逻辑
            # 1. 提取项目Framework关键词
            # 2. 提取项目字符串关键词
            
            framework_keywords = []  # TODO: 实际提取
            string_keywords = []     # TODO: 实际提取
            
            # 设置到过滤系统
            self.filter_system.set_framework_keywords(framework_keywords)
            self.filter_system.set_string_keywords(string_keywords)
            
            # 记录到混淆日志
            self.obfuscation_logger.set_framework_keywords(framework_keywords)
            self.obfuscation_logger.set_string_keywords(string_keywords)
            
            self.progress_logger.complete_step("项目关键词预提取", 
                                             len(framework_keywords) + len(string_keywords))
            return True
            
        except Exception as e:
            self.progress_logger.log_error(f"项目关键词提取失败: {e}")
            return False

    def _step4_extract_code_elements(self) -> bool:
        """步骤4: 各类型关键词提取"""
        self.progress_logger.start_step("各类型关键词提取")

        try:
            # 获取启用的提取器
            extractors = self.extractor_registry.get_ordered_extractors()

            total_mappings = {}

            for extractor in extractors:
                self.progress_logger.update_substep(f"提取{extractor.get_category()}")

                # TODO: 实现文件遍历和提取逻辑
                # 这里需要遍历项目中的所有相关文件，调用提取器进行提取

                # 占位符：模拟提取结果
                extracted_items = []  # TODO: 实际提取

                # 过滤和生成映射
                mappings = extractor.generate_mappings(
                    extracted_items,
                    self.name_generator
                )

                total_mappings[extractor.get_category()] = mappings

                # 记录映射到日志
                for original, obfuscated in mappings.items():
                    self.obfuscation_logger.log_mapping(
                        extractor.get_category(),
                        original,
                        obfuscated
                    )

            self.processing_results['mappings'] = total_mappings
            total_count = sum(len(mappings) for mappings in total_mappings.values())

            self.progress_logger.complete_step("各类型关键词提取", total_count)
            return True

        except Exception as e:
            self.progress_logger.log_error(f"关键词提取失败: {e}")
            return False

    def _step5_filter_and_validate(self) -> bool:
        """步骤5: 过滤与验证"""
        self.progress_logger.start_step("过滤与验证")

        try:
            # 获取过滤统计信息
            filter_stats = self.filter_system.get_filter_stats()

            self.progress_logger.update_substep(f"过滤检查: {filter_stats.get('total_checked', 0)} 项")
            self.progress_logger.update_substep(f"通过过滤: {filter_stats.get('passed', 0)} 项")

            self.processing_results['filter_stats'] = filter_stats

            self.progress_logger.complete_step("过滤与验证", filter_stats.get('passed', 0))
            return True

        except Exception as e:
            self.progress_logger.log_error(f"过滤验证失败: {e}")
            return False

    def _step6_generate_names(self) -> bool:
        """步骤6: 名称生成"""
        self.progress_logger.start_step("名称生成")

        try:
            # 获取生成统计信息
            generation_stats = self.name_generator.get_generation_stats()

            self.progress_logger.update_substep(f"生成名称: {generation_stats.get('unique_names', 0)} 个")

            self.processing_results['generation_stats'] = generation_stats

            self.progress_logger.complete_step("名称生成", generation_stats.get('unique_names', 0))
            return True

        except Exception as e:
            self.progress_logger.log_error(f"名称生成失败: {e}")
            return False

    def _step7_replace_and_output(self) -> bool:
        """步骤7: 代码替换与输出"""
        self.progress_logger.start_step("代码替换与输出")

        try:
            # TODO: 实现代码替换逻辑
            # 1. 批量文件内容替换
            # 2. 项目文件结构调整
            # 3. 混淆日志生成

            self.progress_logger.update_substep("替换代码内容")
            # TODO: 实际替换逻辑

            self.progress_logger.update_substep("生成混淆日志")
            self.obfuscation_logger.export_logs()

            self.progress_logger.complete_step("代码替换与输出")
            return True

        except Exception as e:
            self.progress_logger.log_error(f"代码替换失败: {e}")
            return False

    def _finalize_processing(self):
        """完成处理，生成最终报告"""
        try:
            # 计算总体统计信息
            self.processing_results['extractor_stats'] = self.extractor_registry.get_all_stats()
            self.processing_results['filter_stats'] = self.filter_system.get_filter_stats()
            self.processing_results['generation_stats'] = self.name_generator.get_generation_stats()

            # 记录处理完成
            self.obfuscation_logger.log_process_step(
                "混淆处理完成",
                "COMPLETE",
                result_count=sum(len(mappings) for mappings in self.processing_results.get('mappings', {}).values())
            )

        except Exception as e:
            self.logger.error(f"完成处理时出错: {e}")

    def get_processing_results(self) -> Dict[str, Any]:
        """获取处理结果"""
        return self.processing_results.copy()

    def is_project_processing(self) -> bool:
        """检查是否有项目正在处理"""
        return self.is_processing

    def get_current_project_path(self) -> Optional[str]:
        """获取当前处理的项目路径"""
        return self.current_project_path

    def get_output_directory(self) -> Optional[str]:
        """获取输出目录"""
        return self.output_directory

    def validate_configuration(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证配置
            if not self.config.validate_config():
                return False

            # 验证提取器注册中心
            if not self.extractor_registry.validate_registry():
                return False

            self.logger.info("配置验证通过")
            return True

        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False

    def __str__(self) -> str:
        """返回处理器的字符串表示"""
        status = "processing" if self.is_processing else "idle"
        return f"ObfuscationProcessor(status={status})"

    def __repr__(self) -> str:
        """返回处理器的详细字符串表示"""
        return (f"ObfuscationProcessor(status={'processing' if self.is_processing else 'idle'}, "
                f"project={self.current_project_path})")


# 创建默认的混淆处理器实例
default_processor = ObfuscationProcessor()
