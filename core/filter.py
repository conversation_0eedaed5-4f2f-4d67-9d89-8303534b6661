"""
过滤器系统

判断哪些标识符需要混淆，基于00001项目的oc_tool.py中的is_word_can_mix逻辑实现。
基于ARCHITECTURE.md中的过滤器系统设计。
"""

import os
import re
from typing import Set, List, Dict, Any, Optional
from pathlib import Path

from ..config import ConfigManager
from ..logging import Logger


class FilterSystem:
    """
    过滤器系统类
    
    功能:
    - 判断标识符是否应该被混淆
    - 基于多种过滤规则进行判断
    - 完全复用00001项目的is_word_can_mix逻辑
    - 支持用户自定义过滤规则
    
    过滤规则优先级:
    1. 系统框架关键词过滤 (最高优先级)
    2. 保留关键词过滤
    3. 用户白名单/黑名单
    4. 字符串中关键词处理
    5. 文件类型过滤
    """
    
    def __init__(self, config: Optional[ConfigManager] = None):
        """
        初始化过滤器系统
        
        Args:
            config: 配置管理器，如果为None则使用默认配置
        """
        self.config = config
        self.logger = Logger("FilterSystem")
        
        # 各种关键词集合
        self.system_keywords: Set[str] = set()      # 系统框架关键词
        self.reserved_keywords: Set[str] = set()    # 保留关键词
        self.user_whitelist: Set[str] = set()       # 用户白名单 (强制混淆)
        self.user_blacklist: Set[str] = set()       # 用户黑名单 (强制不混淆)
        
        # 项目特定关键词
        self.framework_keywords: Set[str] = set()   # 项目框架关键词
        self.string_keywords: Set[str] = set()      # 项目字符串关键词
        
        # 过滤统计
        self.filter_stats = {
            'total_checked': 0,
            'system_filtered': 0,
            'reserved_filtered': 0,
            'blacklist_filtered': 0,
            'whitelist_passed': 0,
            'string_filtered': 0,
            'passed': 0
        }
        
        # 加载关键词
        self._load_keywords()
        
        self.logger.info("过滤器系统初始化完成")
    
    def _load_keywords(self):
        """加载各种关键词列表"""
        try:
            if self.config:
                keywords_config = self.config.get_keywords_config()
                
                # 加载系统框架关键词
                system_file = keywords_config.get('system_framework_file')
                if system_file:
                    self._load_system_keywords(system_file)
                
                # 加载保留关键词
                reserved_file = keywords_config.get('reserved_keywords_file')
                if reserved_file:
                    self._load_reserved_keywords(reserved_file)
                
                # 加载用户自定义列表
                self.user_whitelist.update(keywords_config.get('user_whitelist', []))
                self.user_blacklist.update(keywords_config.get('user_blacklist', []))
            
            self.logger.info(f"关键词加载完成 - 系统: {len(self.system_keywords)}, "
                           f"保留: {len(self.reserved_keywords)}, "
                           f"白名单: {len(self.user_whitelist)}, "
                           f"黑名单: {len(self.user_blacklist)}")
                           
        except Exception as e:
            self.logger.error(f"加载关键词失败: {e}")
    
    def _load_system_keywords(self, filename: str):
        """
        加载系统框架关键词
        
        Args:
            filename: 关键词文件名
        """
        try:
            # 构建文件路径
            if self.config:
                config_dir = Path(self.config.default_config_path).parent
                keywords_dir = config_dir / "keywords"
                file_path = keywords_dir / filename
            else:
                # 使用默认路径
                current_dir = Path(__file__).parent.parent
                file_path = current_dir / "config" / "keywords" / filename
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            self.system_keywords.add(line)
                
                self.logger.debug(f"加载系统关键词: {len(self.system_keywords)} 个")
            else:
                self.logger.warning(f"系统关键词文件不存在: {file_path}")
                
        except Exception as e:
            self.logger.error(f"加载系统关键词失败: {e}")
    
    def _load_reserved_keywords(self, filename: str):
        """
        加载保留关键词
        
        Args:
            filename: 关键词文件名
        """
        try:
            # 构建文件路径
            if self.config:
                config_dir = Path(self.config.default_config_path).parent
                keywords_dir = config_dir / "keywords"
                file_path = keywords_dir / filename
            else:
                # 使用默认路径
                current_dir = Path(__file__).parent.parent
                file_path = current_dir / "config" / "keywords" / filename
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            self.reserved_keywords.add(line)
                
                self.logger.debug(f"加载保留关键词: {len(self.reserved_keywords)} 个")
            else:
                self.logger.warning(f"保留关键词文件不存在: {file_path}")
                
        except Exception as e:
            self.logger.error(f"加载保留关键词失败: {e}")
    
    def should_obfuscate(self, name: str, category: str = "") -> bool:
        """
        判断是否应该混淆该标识符
        
        Args:
            name: 标识符名称
            category: 标识符类别 (如: properties, methods, classes等)
            
        Returns:
            是否应该混淆
            
        注意: 这是核心过滤逻辑，基于00001项目的is_word_can_mix函数实现
        """
        self.filter_stats['total_checked'] += 1
        
        # TODO: 这是占位符实现，需要基于00001项目的oc_tool.py中的is_word_can_mix逻辑完善
        
        try:
            # 1. 用户白名单检查 (最高优先级，强制混淆)
            if name in self.user_whitelist:
                self.filter_stats['whitelist_passed'] += 1
                self.logger.debug(f"白名单通过: {name}")
                return True
            
            # 2. 用户黑名单检查 (强制不混淆)
            if name in self.user_blacklist:
                self.filter_stats['blacklist_filtered'] += 1
                self.logger.debug(f"黑名单过滤: {name}")
                return False
            
            # 3. 系统框架关键词检查
            if name in self.system_keywords:
                self.filter_stats['system_filtered'] += 1
                self.logger.debug(f"系统关键词过滤: {name}")
                return False
            
            # 4. 保留关键词检查
            if name in self.reserved_keywords:
                self.filter_stats['reserved_filtered'] += 1
                self.logger.debug(f"保留关键词过滤: {name}")
                return False
            
            # 5. 项目框架关键词检查
            if name in self.framework_keywords:
                self.filter_stats['system_filtered'] += 1
                self.logger.debug(f"项目框架关键词过滤: {name}")
                return False
            
            # 6. 字符串中关键词检查
            if name in self.string_keywords:
                self.filter_stats['string_filtered'] += 1
                self.logger.debug(f"字符串关键词过滤: {name}")
                return False
            
            # 7. 特殊规则检查 (基于类别)
            if not self._check_category_rules(name, category):
                return False
            
            # 8. 基础验证
            if not self._validate_name(name):
                return False
            
            # 通过所有检查，可以混淆
            self.filter_stats['passed'] += 1
            self.logger.debug(f"通过过滤: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"过滤检查失败 {name}: {e}")
            return False
    
    def _check_category_rules(self, name: str, category: str) -> bool:
        """
        检查特定类别的规则
        
        Args:
            name: 标识符名称
            category: 类别
            
        Returns:
            是否通过检查
        """
        if not self.config:
            return True
        
        obfuscation_rules = self.config.get_obfuscation_rules()
        
        # init方法检查
        if category == "methods" and name.startswith("init"):
            preserve_init = obfuscation_rules.get('preserve_init_methods', True)
            if preserve_init:
                self.logger.debug(f"保留init方法: {name}")
                return False
        
        # delegate方法检查
        if category == "methods":
            preserve_delegate = obfuscation_rules.get('preserve_delegate_methods', True)
            if preserve_delegate and self._is_delegate_method(name):
                self.logger.debug(f"保留delegate方法: {name}")
                return False
        
        # 系统方法检查
        if category == "methods":
            preserve_system = obfuscation_rules.get('preserve_system_methods', True)
            if preserve_system and self._is_system_method(name):
                self.logger.debug(f"保留系统方法: {name}")
                return False
        
        return True
    
    def _is_delegate_method(self, name: str) -> bool:
        """
        判断是否为delegate方法
        
        Args:
            name: 方法名
            
        Returns:
            是否为delegate方法
        """
        # TODO: 基于00001项目的逻辑完善
        delegate_patterns = [
            r'.*Delegate.*',
            r'.*DataSource.*',
            r'.*Should.*',
            r'.*Will.*',
            r'.*Did.*'
        ]
        
        for pattern in delegate_patterns:
            if re.match(pattern, name, re.IGNORECASE):
                return True
        
        return False
    
    def _is_system_method(self, name: str) -> bool:
        """
        判断是否为系统方法
        
        Args:
            name: 方法名
            
        Returns:
            是否为系统方法
        """
        system_method_prefixes = [
            'view', 'application', 'scene', 'window',
            'table', 'collection', 'scroll', 'navigation'
        ]
        
        for prefix in system_method_prefixes:
            if name.lower().startswith(prefix.lower()):
                return True
        
        return False
    
    def _validate_name(self, name: str) -> bool:
        """
        验证名称的基本有效性
        
        Args:
            name: 名称
            
        Returns:
            是否有效
        """
        # 基础验证
        if not name or not name.strip():
            return False
        
        # 长度检查
        if self.config:
            rules = self.config.get_obfuscation_rules()
            min_length = rules.get('min_name_length', 1)
            max_length = rules.get('max_name_length', 100)
            
            if len(name) < min_length or len(name) > max_length:
                return False
        
        # 字符检查
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', name):
            return False
        
        return True

    def set_framework_keywords(self, keywords: List[str]):
        """
        设置项目框架关键词

        Args:
            keywords: 框架关键词列表
        """
        self.framework_keywords = set(keywords)
        self.logger.info(f"设置项目框架关键词: {len(keywords)} 个")

    def set_string_keywords(self, keywords: List[str]):
        """
        设置项目字符串关键词

        Args:
            keywords: 字符串关键词列表
        """
        self.string_keywords = set(keywords)
        self.logger.info(f"设置项目字符串关键词: {len(keywords)} 个")

    def get_filter_reason(self, name: str, category: str = "") -> Optional[str]:
        """
        获取过滤原因 (用于日志记录)

        Args:
            name: 标识符名称
            category: 类别

        Returns:
            过滤原因，如果可以混淆则返回None
        """
        # 检查各种过滤条件并返回原因
        if name in self.user_blacklist:
            return "用户黑名单"

        if name in self.system_keywords:
            return "系统框架关键词"

        if name in self.reserved_keywords:
            return "保留关键词"

        if name in self.framework_keywords:
            return "项目框架关键词"

        if name in self.string_keywords:
            return "字符串关键词"

        if not self._check_category_rules(name, category):
            if category == "methods" and name.startswith("init"):
                return "init方法保留"
            elif category == "methods" and self._is_delegate_method(name):
                return "delegate方法保留"
            elif category == "methods" and self._is_system_method(name):
                return "系统方法保留"
            else:
                return "类别规则限制"

        if not self._validate_name(name):
            return "名称格式无效"

        return None  # 可以混淆

    def get_filter_stats(self) -> Dict[str, Any]:
        """获取过滤统计信息"""
        stats = self.filter_stats.copy()

        # 计算百分比
        total = stats['total_checked']
        if total > 0:
            stats['system_filtered_percent'] = (stats['system_filtered'] / total) * 100
            stats['reserved_filtered_percent'] = (stats['reserved_filtered'] / total) * 100
            stats['passed_percent'] = (stats['passed'] / total) * 100

        return stats

    def get_keyword_counts(self) -> Dict[str, int]:
        """获取各类关键词数量统计"""
        return {
            'system_keywords': len(self.system_keywords),
            'reserved_keywords': len(self.reserved_keywords),
            'user_whitelist': len(self.user_whitelist),
            'user_blacklist': len(self.user_blacklist),
            'framework_keywords': len(self.framework_keywords),
            'string_keywords': len(self.string_keywords)
        }

    def __str__(self) -> str:
        """返回过滤器的字符串表示"""
        return f"FilterSystem(keywords={sum(self.get_keyword_counts().values())})"


# 创建默认的过滤器系统实例
default_filter_system = FilterSystem()
