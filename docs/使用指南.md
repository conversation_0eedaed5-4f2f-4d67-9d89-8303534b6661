# Objective-C混淆工具 - 使用指南

## 概述

本工具提供两种使用方式：
- **GUI模式**：图形界面，适合可视化操作
- **CLI模式**：命令行模式，适合自动化和脚本集成

## 快速启动

### GUI模式（推荐新手）
```bash
./scripts/启动GUI.sh
```

### CLI模式（推荐高级用户）
```bash
./scripts/运行混淆工具.sh
```

## CLI模式详细说明

### 基本使用
```bash
# 查看帮助
python oc_cli.py --help

# 使用默认配置
python oc_cli.py

# 指定SDK地区
python oc_cli.py --sdk-region 2    # 海外版本
python oc_cli.py --sdk-region 1    # 国内版本
```

### 完整参数示例

#### 海外版本
```bash
python oc_cli.py \
  --input-path /path/to/project \
  --sdk-region 2 \
  --new-project-name MyOverseasProject \
  --sdk-options "AppsFlyer,Facebook,Firebase" \
  --functions 0
```

#### 国内版本
```bash
python oc_cli.py \
  --input-path /path/to/project \
  --sdk-region 1 \
  --new-project-name MyDomesticProject \
  --sdk-options "ShanYanSDK,BDASignalManager" \
  --functions 0
```

### 参数说明

#### 基本参数
- `--sdk-region, -r`: SDK地区（1=国内，2=海外）
- `--input-path, -i`: 工程路径
- `--new-project-name, -n`: 新工程名
- `--functions, -f`: 执行功能（0=全部，t=推荐）

#### SDK选项
**国内版本**：`ShanYanSDK,BDASignalManager`
**海外版本**：`AppsFlyer,Facebook,Firebase`

#### 功能选项
- `0`: 全部操作
- `t`: 推荐操作（日常使用）
- `1-9,a-j,u`: 具体功能（详见help）

## 环境要求

### 依赖安装
```bash
# 自动安装所有依赖
./scripts/安装依赖包.sh
```

### 手动安装
```bash
source venv/bin/activate
pip install -r requirements.txt
```

## 构建部署

### Linux构建
```bash
./scripts/deploy-linux.sh
```

### macOS构建
```bash
# CLI版本
./scripts/build_cli.sh

# GUI版本  
./scripts/build_gui.sh
```

## 常见问题

### 模块找不到错误
```bash
# ❌ 错误：直接运行
python3 oc_cli.py

# ✅ 正确：使用脚本
./scripts/运行混淆工具.sh
```

### PIL兼容性问题（Linux）
```bash
# 使用兼容性修复
./scripts/fix-pil-compatibility.sh
./dist/oc_cli_fixed --help
```

## 使用建议

### 新手推荐
1. 使用GUI模式熟悉功能
2. 使用`--functions t`进行日常混淆
3. 生产环境使用`--functions 0`完整混淆

### 高级用户
1. 编写自定义脚本自动化流程
2. 集成到CI/CD管道
3. 根据需求自定义功能组合

## 技术支持

遇到问题请查看`docs/`目录下的详细文档或提交issue。 