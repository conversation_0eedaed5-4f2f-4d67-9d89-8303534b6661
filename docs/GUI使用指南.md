# GUI使用指南

## 概述

图形界面版本提供直观的可视化操作，适合不熟悉命令行的用户使用。

## 启动GUI

### macOS启动
```bash
# 方式1：使用启动脚本
./scripts/启动GUI.sh

# 方式2：直接运行
python oc_gui.py

# 方式3：使用打包版本
./dist/oc_gui.app
```

### 功能特性

#### 1. 配置面板
- **项目路径选择**：浏览选择Xcode项目目录
- **SDK地区设置**：选择国内/海外版本
- **SDK选项配置**：勾选需要的SDK组件
- **功能选择**：选择要执行的混淆功能

#### 2. 实时日志
- **执行过程显示**：实时显示混淆进度
- **错误信息提示**：及时显示错误和警告
- **完成状态反馈**：显示操作结果

#### 3. 便捷操作
- **一键重置**：快速清空所有配置
- **配置保存**：记住用户偏好设置
- **批量操作**：支持多项功能同时执行

## 界面说明

### 主要区域
1. **配置区域**（上方）
   - 项目路径输入框和浏览按钮
   - SDK地区选择（单选框）
   - SDK选项勾选（复选框）
   - 功能选择（复选框）

2. **控制区域**（中间）
   - 开始混淆按钮
   - 重置配置按钮
   - 退出程序按钮

3. **日志区域**（下方）
   - 滚动文本框显示执行日志
   - 支持文本选择和复制

### 操作流程
1. **选择项目**：点击"浏览"选择Xcode项目
2. **配置SDK**：选择地区和所需SDK组件
3. **选择功能**：勾选要执行的混淆功能
4. **开始执行**：点击"开始混淆"按钮
5. **查看结果**：在日志区域查看执行过程

## 配置说明

### SDK地区选择
- **国内版本**：集成国内SDK（闪验、巨量归因等）
- **海外版本**：集成海外SDK（AppsFlyer、Facebook等）

### 常用功能组合
- **快速测试**：选择"删除注释"、"删除打印"、"修改工程名"
- **完整混淆**：选择"执行全部操作"
- **自定义组合**：根据需求选择特定功能

## 常见问题

### 启动问题
**问题**：GUI无法启动
```bash
# 解决方案1：检查依赖
./scripts/安装依赖包.sh

# 解决方案2：手动启动
source venv/bin/activate
python oc_gui.py
```

### 功能问题
**问题**：混淆功能执行失败
- 检查项目路径是否正确
- 确保项目包含`.xcodeproj`文件
- 查看日志区域的错误信息

### 路径问题
**问题**：找不到配置文件
- 确保`配置文件`目录存在
- 检查目录权限设置
- 重新安装或重置配置

## 高级功能

### 配置文件管理
GUI会自动保存用户配置：
- 上次使用的项目路径
- SDK选择偏好
- 功能选择习惯

### 批量处理
支持对多个项目进行相同配置的批量处理：
1. 配置好第一个项目
2. 记录配置参数
3. 对其他项目重复应用

### 日志导出
日志信息可以复制用于：
- 错误报告
- 操作记录
- 问题诊断

## 性能优化

### 响应速度
- GUI采用异步处理，避免界面卡死
- 大型项目处理时显示进度条
- 支持操作中断和恢复

### 内存使用
- 自动清理临时文件
- 优化内存占用
- 支持大型项目处理

## 故障排除

### 常见错误
1. **模块导入错误**：使用启动脚本而非直接运行
2. **权限错误**：确保对项目目录有写权限
3. **路径错误**：使用绝对路径或确保相对路径正确

### 调试模式
```bash
# 启用调试模式
python oc_gui.py --debug
```

### 日志分析
关注日志中的关键信息：
- `ERROR`: 严重错误，需要处理
- `WARNING`: 警告信息，可能影响结果
- `INFO`: 一般信息，显示执行进度

## 更新说明

### 版本特性
- **v2.0**: 全新界面设计，增强稳定性
- **v1.x**: 基础功能实现

### 升级建议
1. 备份重要配置
2. 下载最新版本
3. 重新配置偏好设置
4. 测试基本功能

## 技术支持

### 获取帮助
1. 查看日志输出的错误信息
2. 参考命令行版本的错误处理
3. 检查系统环境和依赖

### 反馈问题
提供以下信息以便诊断：
- 操作系统版本
- Python版本
- 错误日志内容
- 复现步骤 