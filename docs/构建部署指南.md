# 构建部署指南

## 概述

本工具支持多平台构建，包括macOS和Linux系统的CLI/GUI版本。

## 构建环境要求

### 通用要求
- Python 3.6+
- 虚拟环境支持
- 网络连接（下载依赖）

### macOS要求
- Xcode Command Line Tools
- PyInstaller 4.10+

### Linux要求
- GCC编译器
- Python开发包
- 图像处理库开发包

### CentOS 7兼容构建要求
- Python 3.6+
- PyInstaller支持
- 网络连接（下载依赖）

## 快速构建

### macOS构建
```bash
# CLI版本（命令行工具）
./scripts/build_cli.sh

# GUI版本（图形界面应用）
./scripts/build_gui.sh

# 通用构建工具
python scripts/build_universal.py
```

### Linux构建
```bash
# Linux通用构建
./scripts/deploy-linux.sh

# CentOS 7兼容构建（解决PIL问题）
./scripts/build-centos7-no-pil.sh
```

### 依赖管理
```bash
# 安装项目依赖
./scripts/安装依赖包.sh

# 解决网络问题
./scripts/解决网络问题.sh
```

## 构建输出

### macOS输出
- **CLI**: `dist/oc_cli` - 命令行可执行文件
- **GUI**: `dist/oc_gui.app` - macOS应用包

### Linux输出
- **CLI**: `dist/oc_cli` - Linux可执行文件
- **CentOS 7兼容**: `dist/oc_cli` + `dist/CentOS7无PIL兼容性说明.txt` - 无PIL问题版本

## 部署说明

### macOS部署
```bash
# 移动到应用目录
mv dist/oc_gui.app /Applications/

# 或者直接运行
./dist/oc_cli --help
```

### Linux部署
```bash
# 设置执行权限
chmod +x dist/oc_cli

# 运行
./dist/oc_cli --help
```

### CentOS/RHEL兼容性

#### 方法一：无PIL构建（推荐）
```bash
# 构建无PIL版本，彻底解决兼容性问题
./scripts/build-centos7-no-pil.sh

# 部署到服务器
scp dist/oc_cli user@server:/path/
```

#### 方法二：兼容性修复
如遇到PIL相关错误：
```bash
# 生成兼容性包装脚本
./scripts/fix-pil-compatibility.sh

# 使用兼容版本
./dist/oc_cli_fixed --help
```

## CentOS 7兼容性解决方案

### PIL兼容性问题
PyInstaller在不同Linux环境下的PIL库存在兼容性问题，主要表现为：

#### 问题症状
- `ELF load command address/offset not properly aligned`
- PIL模块加载失败
- 在CentOS 7上无法启动

#### 解决方案
使用无PIL构建脚本，完全避免PIL兼容性问题：

#### 构建过程
```bash
# 1. 运行无PIL构建脚本
./scripts/build-centos7-no-pil.sh

# 2. 脚本会自动：
#    - 备份原始代码
#    - 修改图片处理逻辑，移除PIL依赖
#    - 构建兼容版本
#    - 恢复原始代码
```

#### 优势
- 彻底解决PIL兼容性问题
- 保持图片加密功能完整
- 更好的跨平台兼容性
- 更小的文件大小

## 打包配置

### PyInstaller配置
核心构建参数：
```bash
pyinstaller \
    --onefile \
    --name="oc_cli" \
    --console \
    --add-data "ObjectiveC:ObjectiveC" \
    --add-data "配置文件:配置文件" \
    --exclude-module tkinter \
    --exclude-module matplotlib \
    --clean \
    oc_cli.py
```

### 优化选项
- `--onefile`: 单文件打包
- `--clean`: 清理缓存
- `--strip`: 减小文件大小
- `--exclude-module`: 排除不需要的模块

## 常见问题

### 构建失败
1. **依赖缺失**：运行`./scripts/安装依赖包.sh`
2. **权限问题**：确保脚本有执行权限
3. **网络问题**：使用`./scripts/解决网络问题.sh`

### 运行问题
1. **模块找不到**：确保在虚拟环境中构建
2. **路径错误**：检查配置文件路径
3. **权限错误**：设置可执行权限

### Linux特定问题
1. **PIL错误**：使用兼容性修复脚本
2. **GLIBC版本**：在目标系统相近的环境中构建
3. **系统库缺失**：安装开发包

## 自动化构建

### CI/CD集成
```bash
# 在构建服务器上
git clone <repository>
cd <project>
./scripts/安装依赖包.sh
./scripts/deploy-linux.sh

# 打包构建产物
tar -czf oc_cli_linux.tar.gz dist/oc_cli
```

### 批量构建
```bash
# 同时构建多个版本
./scripts/build_cli.sh && ./scripts/build_gui.sh
```

## 版本管理

### 版本号更新
构建前确保更新版本信息：
1. 更新`oc_cli.py`中的版本号
2. 更新`oc_gui.py`中的版本号
3. 更新配置文件中的版本信息

### 发布准备
```bash
# 清理旧构建
rm -rf build/ dist/

# 重新构建
./scripts/build_cli.sh

# 测试构建结果
./dist/oc_cli --version
```

## 技术细节

### 文件包含策略
- `ObjectiveC/`: 核心混淆逻辑
- `配置文件/`: 配置模板和规则
- 排除测试和开发工具模块

### 性能优化
- 使用`--strip`减小文件大小
- 排除不必要的模块
- 优化依赖加载顺序

### 兼容性处理
- macOS: 支持Intel和Apple Silicon
- Linux: 兼容主流发行版
- Python: 支持3.6-3.11

## 故障排除

### 调试模式
```bash
# 启用详细输出
export PYTHONPATH=.
python -v oc_cli.py --help
```

### 日志分析
构建过程中的关键日志：
- 依赖安装日志
- PyInstaller构建日志
- 模块导入错误
- 路径解析问题

### 获取帮助
1. 查看构建日志
2. 检查系统环境
3. 验证依赖完整性
4. 参考平台特定说明 