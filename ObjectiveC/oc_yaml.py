"""
统一配置文件
"""
import os, yaml

def init():
    # 配置文件句柄
    # global yaml_manager
    from . import oc_util
    yaml_path = oc_util.get_resource_path('ObjectiveC/oc_config.yaml')
    file_obj = open(yaml_path, 'r', encoding="utf-8")
    file_data = file_obj.read()
    file_obj.close()
    yaml_manager = yaml.load(file_data, Loader=yaml.FullLoader)

    # 过滤的项目
    global list_filter_project
    list_filter_project = yaml_manager['FilterProjectList']

    # 此文件夹名不修改, 对文件夹内的文件不控制
    global list_folder_not_change
    list_folder_not_change = list(yaml_manager['FolderNotChangedList'])

    # 此文件夹名不修改, 且文件夹内的所有文件均不修改
    global list_folder_ignore_all
    list_folder_ignore_all = list(yaml_manager['FolderIgnoreAllFiles'])

    # 文件夹名保留关键词
    global list_keep_word_for_folder
    list_keep_word_for_folder = list(yaml_manager['KeepWordForFolder'])

    # 不删除注释/打印的文件
    global list_not_delete_print_or_annotation
    list_not_delete_print_or_annotation = list(yaml_manager['DeletePrintOrAnnotationIgnoreFileList'])

    # 不修改的文件名列表
    global list_file_not_change
    list_file_not_change = list(yaml_manager['IgnoreFileList'])

    # 支持修改文件名类型
    global list_support_file_type
    list_support_file_type = list(yaml_manager['SupportModifyFileType'])

    # 用户自定义不可修改关键词
    global list_user_custom_keyword_not_change
    list_user_custom_keyword_not_change = list(yaml_manager['UserCustomKeywordList'])

    # 不提取内容的文件
    global list_not_extract_content_file
    list_not_extract_content_file = list(yaml_manager['NotExtractContentFileList'])

    # 关键词在字符串中,但是用户允许修改
    # 用户自定义允许修改关键词
    global list_user_arrow_change_keyword
    list_user_arrow_change_keyword = list(yaml_manager['UserArrowModifyKeywordList'])
    read_user_custom_white_list()

    # (文件名)(方法名)(属性名)(全局变量)(Block)(代理)(枚举)(常量名)(图片名)保留关键词
    global list_keep_word
    list_keep_word = list(yaml_manager['KeepWordForAll'])

    # 需要混淆的通知名列表
    global list_need_change_notification_name
    list_need_change_notification_name = list(yaml_manager['NeedChangeNotificationNameList'])

    # 支持打开查找替换属性方法等文件的类型
    global list_support_open_file_type
    list_support_open_file_type = list(yaml_manager['SupportOpenFileType'])

    # 图片文件夹列表 和 支持的类型
    global list_image_folder
    list_image_folder = list(yaml_manager['ImageFolder'])
    global list_image_type
    list_image_type = list(yaml_manager['SupportImageType'])

def read_user_custom_white_list():
    ''' 读取用户白名单添加到用户允许修改的列表中 '''
    from . import oc_util
    file_path = oc_util.get_config_file_path('user_custom_white_list_XXGPlayKit.txt')
    if os.path.isfile(file_path):
        for line in open(file_path, "r"):
            if "\n" in line:
                line = str(line).replace("\n","")
            if line not in list_user_arrow_change_keyword:
                list_user_arrow_change_keyword.append(line)

if __name__ == '__main__':
    file_path = os.getcwd() + '/20000.txt'
    aList = []
    if os.path.isfile(file_path):
        for line in open(file_path, "r"):
            if "\n" in line:
                line = str(line).replace("\n","")
            line = line.strip()
            if len(line) == 3:
                print(line)
    
