"""
本次混淆所有配置信息
"""
import os, yaml, random, json
import sys

def get_resource_path(relative_path):
    """
    获取资源文件的正确路径，兼容PyInstaller打包和开发环境
    """
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后的临时目录
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    return os.path.join(base_path, relative_path)

def get_config_file_path(filename):
    """
    获取配置文件的完整路径
    """
    return get_resource_path(os.path.join("配置文件", "ObjectiveC配置文件", filename))

def init():
    # 本次混淆路径
    global path_mix
    path_mix = ''

    # 本次工程混淆路径
    global path_mix_project
    path_mix_project = ''

    # 系统framework关键词列表
    global list_system_framework_word
    list_system_framework_word = []

    # 工程中framework的关键词列表
    global list_project_framework_word
    list_project_framework_word = []

    # 当前工程名
    global name_current_project
    name_current_project = ''

    # 新的工程名
    global new_project_name
    new_project_name = ''

    # 工程中字符串关键词列表
    global list_string_words
    list_string_words = []

    # 工程中文件夹名列表
    global list_folder_name
    list_folder_name = []

    # 文件夹名类
    global class_folder
    class_folder = Folder()

    # OC文件类
    global class_oc_file
    class_oc_file = FileInfo()

    # 文件夹名前缀、文件名前缀、属性名前缀、方法名前缀
    global list_all_prefix
    list_all_prefix = []
    global prefix_folder
    prefix_folder = random_one_prefix(True, True, 3)
    prefix_folder = ''
    global prefix_file
    prefix_file = random_one_prefix(True, True, 2)
    prefix_file = ''
    global prefix_property
    prefix_property = random_one_prefix(False, True, 2)
    prefix_property = ''
    global prefix_method
    prefix_method = random_one_prefix(False, True, 2)
    prefix_method = ''

    # 随机词库
    global list_three_words
    list_three_words = list(json.load(open(get_config_file_path("00_three.json"))))
    global list_four_words
    list_four_words = list(json.load(open(get_config_file_path("01_four.json"))))
    global list_five_words
    list_five_words = list(json.load(open(get_config_file_path("02_five.json"))))
    global list_six_words
    list_six_words = list(json.load(open(get_config_file_path("03_six.json"))))
    global list_seven_words
    list_seven_words = list(json.load(open(get_config_file_path("04_seven.json"))))
    global list_eight_words
    list_eight_words = list(json.load(open(get_config_file_path("05_eight.json"))))
    global list_nine_words
    list_nine_words = list(json.load(open(get_config_file_path("06_nine.json"))))

    # 工程随机词库
    global list_random_words
    list_random_words = read_words()

    # 被忽略关键词列表
    global list_ignore_keyword
    list_ignore_keyword = []

def read_words():
    path = get_config_file_path("project_name.json")
    return list(json.load(open(path)))

def get_text_lines(file_path):
    ''' 返回文本每一行的列表 '''
    aList = []
    if os.path.isfile(file_path):
        for line in open(file_path, "r"):
            if "\n" in line:
                line = str(line).replace("\n","")
            aList.append(line)
    return aList

def random_one_prefix(is_upper, is_gang, num):
    ''' 
    返回一个随机的前缀 
    is_upper 是否大写
    is_gang 是否带横杠(_)
    num 是几位(不含_)
    '''
    if num <= 0:
        return ''
    prefix = ''
    letters = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
    for _ in range(num):
        prefix = prefix + random.choice(letters)
    if is_upper:
        prefix = prefix.upper()
    else:
        prefix = prefix.lower()
    if is_gang:
        prefix = prefix + '_'
    if prefix in list_all_prefix:
        return random_one_prefix(is_upper, is_gang, num)
    return prefix

class Folder():
    def __init__(self):
        # 列表：原文件夹名和新文件夹名
        self.old_dir_list = []
        self.new_dir_list = []

def three():
    return random.choice(list_three_words)
def four():
    return random.choice(list_four_words)
def five():
    return random.choice(list_five_words)
def six():
    return random.choice(list_six_words)
def seven():
    return random.choice(list_seven_words)
def eight():
    return random.choice(list_eight_words)
def nine():
    return random.choice(list_nine_words)

class FileInfo():
    ''' FileInfo文件内容的信息类 '''
    def __init__(self):
        # 总表包含下面所有元素
        self.total_words = []
        # 所有文件名的列表, 防止未修改文件名时, 有关键词合文件名重复
        self.all_file_name_list = []
        # 新旧文件名列表
        self.file_name_old_list = []
        self.file_name_new_list = []
        # 新旧属性名列表
        self.property_name_old_list = []
        self.property_name_new_list = []
        # 新旧枚举值
        self.enum_old_list = []
        self.enum_new_list = []
        # 新旧typedef定义的block
        self.block_old_list = []
        self.block_new_list = []
        # 新旧常量名
        self.constant_old_list = []
        self.constant_new_list = []
        # 新旧delegate名
        self.delegate_old_list = []
        self.delegate_new_list = []
        # 新旧方法名
        self.method_old_list = []
        self.method_new_list = []
        # 新旧局部变量名
        self.local_variable_old_list = []
        self.local_variable_new_list = []
        # 新旧类名
        self.class_name_old_list = []
        self.class_name_new_list = []
        # 新旧c语言方法名
        self.c_method_old_list = []
        self.c_method_new_list = []

class AddContentInfo():
    ''' 添加垃圾代码信息 '''

if __name__ == "__main__":
    print(11111,os.getcwd())
    