import os, time, sys

# 添加项目根目录到Python路径，确保可以导入ObjectiveC模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from ObjectiveC import oc_util
from ObjectiveC import oc_yaml
from ObjectiveC import oc_tool
from ObjectiveC.oc_custom import custom_a_framework
from ObjectiveC.oc_function import b_project as project
from ObjectiveC.oc_function import c_folder
from ObjectiveC.oc_function import d_string
from ObjectiveC.oc_function import e_print_annotation
from ObjectiveC.oc_function import f_file
from ObjectiveC.oc_function import g_notification
from ObjectiveC.oc_custom import custom_h_search_file_content_message
from ObjectiveC.oc_function import i_property
from ObjectiveC.oc_function import j_enum
from ObjectiveC.oc_function import k_block
from ObjectiveC.oc_function import l_constant
from ObjectiveC.oc_function import m_delegate
from ObjectiveC.oc_function import n_method
from ObjectiveC.oc_function import n_c_method
from ObjectiveC.oc_function import o_image
from ObjectiveC.oc_function import p_uuid
from ObjectiveC.oc_function import q_location
from ObjectiveC.oc_function import s_string
from ObjectiveC.oc_function import t_host
from ObjectiveC.oc_function import v_local_variable
from ObjectiveC.oc_function import u_class
from ObjectiveC.oc_custom import custom_get_params
from ObjectiveC.oc_custom import custom_start
from ObjectiveC.oc_custom import custom_end
from ObjectiveC.oc_custom import custom_util

def init():
    # 初始化配置文件
    oc_util.init()
    # 初始化配置文件
    oc_yaml.init()

    ########### 拷贝工程 ###########
    # 请输入工程的文件夹目录(只允许有一个.xcodeproj)
    input_path = str(input("请输入需要混淆的ObjectiveC工程路径:")).strip()
    # 如果输入为空默认为：/Users/<USER>/jumbo/xianxian/XXGPlayKit
    if input_path == '':
        input_path = '/Users/<USER>/jumbo/xianxian/XXGPlayKit'
    # 生成混淆目录
    current_date = time.strftime("%Y%m%d%H%M%S", time.localtime())
    # 获取当前混淆目录
    mix_path = oc_tool.get_current_mix_path(current_date, input_path)
    print('Objective-C本次混淆目录: ', current_date)
    oc_util.path_mix = mix_path
    oc_util.path_mix_project = mix_path + '/混淆后工程'
    # 获取当前工程名
    oc_util.name_current_project = oc_tool.get_current_project_name()
    print('当前工程名: ', oc_util.name_current_project)
    ########### 拷贝工程 ###########

    ########### 获取参数 ###########
    custom_get_params.get_params()
    ########### 获取参数 ###########

    ########### 初始化工具类 ##########
    oc_tool.init()
    ########### 初始化工具类 ##########

    ########### 准备工作 ###########
    custom_start.init()
    ########### 准备工作 ###########

    # 读取系统framework关键词
    save_system_framework_path = oc_util.get_config_file_path('system_framework_keywords_ios18.2.txt')
    oc_util.list_system_framework_word = oc_util.get_text_lines(save_system_framework_path)

    # 读取工程中的framework关键词 封装起来的方法
    save_project_framework_path = mix_path + '/project_framework_keywords.txt'
    custom_a_framework.init_project(save_project_framework_path, 2)
    # a_framework.init_project(save_project_framework_path, 2)
    oc_util.list_project_framework_word = oc_util.get_text_lines(save_project_framework_path)

    # xib中关键词加入到系统framework关键词
    save_xib_framework_path = oc_util.get_config_file_path('xib_framework_keywords.txt')
    # 将xib中的关键词加入到系统framework关键词中
    oc_util.list_system_framework_word.extend(oc_util.get_text_lines(save_xib_framework_path))

    # 获取工程中字符串的关键词
    save_project_string_path = mix_path + '/project_all_string_words.txt'
    d_string.get_project_string(save_project_string_path)
    oc_util.list_string_words = oc_util.get_text_lines(save_project_string_path)

    # 查找工程信息
    custom_h_search_file_content_message.init()
    # h_search_file_content_message.init()

    # 选择您要使用的功能
    str_func = ''
    str_func = str_func + '0:执行全部操作\n'
    str_func = str_func + 't:执行半全部操作\n'
    str_func = str_func + '1:删除注释\n'
    str_func = str_func + '2:删除打印\n'
    str_func = str_func + '3:修改工程名\n'
    str_func = str_func + '4:修改文件夹名\n'
    str_func = str_func + '5:修改文件名\n'
    str_func = str_func + '6:修改通知名\n'
    str_func = str_func + '7:修改属性名和全局变量名和局部变量名\n'
    str_func = str_func + '8:修改枚举\n'
    str_func = str_func + '9:修改block名\n'
    str_func = str_func + 'a:修改常量名\n'
    str_func = str_func + 'b:修改delegate名\n'
    str_func = str_func + 'c:修改方法名\n'
    str_func = str_func + 'd:图片处理\n'
    str_func = str_func + 'e:修改uuid\n'
    str_func = str_func + 'u:修改类名\n'
    str_func = str_func + 'j:资源加密等\n'
    str_func = str(input('请输入需要使用功能对应的数字或字母\n%s\n'%str_func))
    func = list(set(str_func))
    func.sort()

    # 输出 func
    print('func: ', func)

    if '0' in func:
        func = ['1','2','3','4','5','6','7','8','9','a','u','b','c','d','e','j']
    if 't' in func:
        func = ['1','3','4','5','6','7','8','9','a','u','b','c','d','e']
    if '1' in func:
        print('1删除注释开始...')
        e_print_annotation.delete_print_or_annotation(1)
        print('1删除注释完成')
    if '2' in func:
        print('2删除打印开始...')
        e_print_annotation.delete_print_or_annotation(2)
        print('2删除打印完成')
    if 'd' in func:
        print('d图片处理开始...')
        o_image.init()
        print('d图片处理完成')
    if '3' in func:
        print('3修改工程名开始...')
        project.modify_project_name(oc_util.new_project_name)
        print('3修改工程名完成')
    if '4' in func:
        print('4修改文件夹名开始...')
        c_folder.modify_project_folder_name()
        print('4修改文件夹名完成')
    if '5' in func:
        print('5修改文件名开始...')
        f_file.modify_oc_file_name()
        print('5修改文件名完成')
    if '6' in func:
        print('6修改通知名开始...')
        g_notification.modify_project_notification()
        print('6修改通知名完成')
    if '9' in func:
        print('9修改block名开始...')
        k_block.init()
        print('9修改block名完成')
    if '7' in func:
        print('7修改属性名和全局变量名开始...')
        i_property.init()
        v_local_variable.init()
        print('7修改属性名和全局变量名完成')
    if '8' in func:
        print('8修改枚举开始...')
        j_enum.init()
        print('8修改枚举完成')
    if 'a' in func:
        print('a修改常量名开始...')
        l_constant.init()
        print('a修改常量名完成')
    if 'b' in func:
        print('b修改delegate名开始...')
        m_delegate.init()
        print('b修改delegate名完成')
    if 'u' in func:
        print('u修改类名开始...')
        u_class.init()
        print('u修改类名完成')
    if 'c' in func:
        print('c修改方法名开始...')
        n_method.init()
        n_c_method.init()
        print('c修改方法名完成')
    if 'e' in func:
        print('e修改uuid开始...')
        p_uuid.init()
        print('e修改uuid完成')
    if 'j' in func:
        print('2删除打印开始...')
        e_print_annotation.delete_print_or_annotation(2)
        print('2删除打印完成')
        # 后续完善
        custom_end.init(custom_util.sdk_cn_or_os)

if __name__ == "__main__":
    # 混淆oc代码
    init()

'''
pip3 install pyyaml # 处理yaml文件
pip3 install pbxproj # 处理pbxproj文件
pip3 install numpy # 处理plist文件

pip3 install markdown beautifulsoup4 pygments # 转换md为html
pip3 install Pillow # 处理图片
'''