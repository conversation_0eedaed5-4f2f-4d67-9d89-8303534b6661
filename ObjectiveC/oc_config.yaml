FilterProjectList:
  - 此项目.xcodeproj或.xcworkspace不修改
  - XXGPlayKitOSDemo
  - XXGPlayKitCNDemo
  - XXGThirdMiddlewares
  - XXGPlayKitOSDemo-Pods
  - Pods.xcodeproj
  - XXGPlayKitOSDemo-Pods
  - project
FolderNotChangedList:
  - 此文件夹名不修改, 对文件夹内的文件不控制
  - SDK对接文档
FolderIgnoreAllFiles:
  - 此文件夹名不修改, 且文件夹内的所有文件均不修改
  - Masonry
  - SDWebImage
  - MQTTClient
  - OSFrameworks
  - CNFrameworks
  - OSResources
  - CNResources
  - xcuserdata
  - xcshareddata
  - .git
  - .gitignore
  - Pods
  - scripts
  - log_parser
KeepWordForFolder:
  - 文件夹名保留关键词
SupportModifyFileType:
  - 支持修改文件名类型
  - .h
  - .m
  - .mm
  - .storyboard
  - .xib
  - .pch
  - .swift
  - .md
  - .json
DeletePrintOrAnnotationIgnoreFileList:
  - 不删除注释/打印的文件
  - ViewController.h
  - ViewController.m
  - SceneDelegate.h
  - SceneDelegate.m
  - AppDelegate.h
  - AppDelegate.m
  - _.h
  - _.m
IgnoreFileList:
  - 不修改的文件名列表,文件名不修改，内容不受限制
  - ViewController.h
  - ViewController.m
  - _.h
  - _.m
  - SceneDelegate.h
  - SceneDelegate.m
  - AppDelegate.h
  - AppDelegate.m
  - 闲闲SDK-国内版-iOS接入文档（3.0）.md
  - 闲闲SDK-海外版-iOS接入文档（3.0）.md
  - podfile
  - Podfile.lock
UserCustomKeywordList:
  - 用户自定义不可修改关键词
  - demo_login
  - demo_logout
  - demo_pay
  - demo_uploadRoleInfo
  - demo_openUserCenter
  - demo_bindFacebook
  - demo_bindFVK
  - demo_logEvent
  - demo_log
  - demo_showRewaredAd
  - demo_iapRepair
  - 下面4个是MQTTd的代理
  - sessionManagerReconnect
  - handleMessage
  - onTopic
  - retained
NotExtractContentFileList:
  - 不提取内容的文件
  - _.h
  - _.m
  - ViewController.h
  - ViewController.m
NeedChangeNotificationNameList:
  - 需要修改的通知名字符串
  - 四九_FirstNotification_四九
UserArrowModifyKeywordList:
  - 关键词在字符串中,但是用户允许修改
SupportOpenFileType:
  - 支持打开查找替换属性方法等文件的类型
  - .h
  - .m
  - .mm
  - .xib
  - .pch
  - .storyboard
  - .json
  - .md
ImageFolder:
  - 图片所在的文件夹
  - XXGPlayKit.bundle
SupportImageType:
  - 支持图片的类型
  - .png
  - .jpg
  - .json
KeepWordForAll:
  - (文件夹名)(文件名)(方法名)(属性名)(全局变量)(Block)(代理)(枚举)(常量名)(图片名)保留关键词
  - ---------------------------------------------------(19)
  - ReachabilityManager
  - ---------------------------------------------------(18)
  - CollectionViewCell
  - ---------------------------------------------------(15)
  - ImageMemorySize
  - CompletionBlock
  - ---------------------------------------------------(14)
  - BackgroundView
  - ViewController
  - collectionView
  - SessionManager
  - SecurityPolicy
  - ---------------------------------------------------(13)
  - Serialization
  - ProgressBlock
  - Configuration
  - ---------------------------------------------------(12)
  - TimeInterval
  - ImageManager
  - Certificates
  - ContentTypes
  - ---------------------------------------------------(11)
  - SerialQueue
  - StatusBlock
  - Information
  - ---------------------------------------------------(10)
  - Downloader
  - Dictionary
  - Networking
  - Controller
  - MainThread
  - MainWindow
  - Identifier
  - Serializer
  - ---------------------------------------------------(9)
  - Parameter
  - TextField
  - ToastView
  - AlertView
  - Operation
  - ImageView
  - Downloads
  - Animation
  - Extension
  - ---------------------------------------------------(8)
  - Delegate
  - Protocol
  - Password
  - Progress
  - BoxLabel
  - KeyChain
  - Register
  - Response
  - Complete
  - Interval
  - Instance
  - Accounts
  - Observer
  - WithName
  - ---------------------------------------------------(7)
  - Message
  - Manager
  - Storage
  - Session
  - Network
  - Account
  - Address
  - AtIndex
  - Handler
  - Headers
  - Clicked
  - Control
  - Loading
  - Options
  - Decoder
  - Request
  - Receipt
  - ForType
  - ---------------------------------------------------(6)
  - Status
  - Config
  - Canvas
  - Define
  - Device
  - Encode
  - Button
  - Window
  - Helper
  - Header
  - Images
  - Values
  - Resume
  - Result
  - Rotate
  - Amount
  - Radius
  - Action
  - Format
  - Center
  - ---------------------------------------------------(5)
  - Array
  - Block
  - Bytes
  - Coder
  - Cache
  - Color
  - Count
  - Frame
  - Field
  - Image
  - Index
  - Level
  - Layer
  - Label
  - Login
  - Model
  - Stage
  - Style
  - State
  - Scale
  - Timer
  - Toast
  - Token
  - Tasks
  - Width
  - Queue
  - Value
  - ---------------------------------------------------(4)
  - View
  - Info
  - Icon
  - Cell
  - Code
  - Type
  - Tool
  - Time
  - Task
  - Text
  - Util
  - Mode
  - Rect
  - Name
  - Data
  - Date
  - Dict
  - Size
  - Edge
  - List
  - File
  - Path
  - Json
  - ---------------------------------------------------(3)
  - Btn
  - Bar
  - Key
  - Num
  - HUD
  - ----------------------------------------------------(自定义)