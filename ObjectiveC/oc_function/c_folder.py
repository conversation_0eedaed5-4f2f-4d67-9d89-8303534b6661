"""
修改文件夹名
"""
import os, random
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml

def modify_project_folder_name():
    path = oc_util.path_mix_project
    rename_folder(path)
    replace_xcodeproj_file(path)
def rename_folder(path):
    for root, dirs, _ in os.walk(path, topdown=True):
        dirs[:] = [d for d in dirs if isTrue(d)]
        for name in dirs:
            # 检查完全匹配
            if name in list(oc_yaml.list_filter_project):
                continue
            # 检查去掉后缀后是否匹配
            name_without_ext = name.split('.')[0] if '.' in name else name
            if name_without_ext in list(oc_yaml.list_filter_project):
                continue
            if name in list(oc_yaml.list_folder_not_change):
                continue
            if name == oc_util.name_current_project:
                continue
            folder_path = os.path.join(root, name)
            new_name = random_one_new_folder_name(name)
            name = name.replace(name, new_name)
            new_path = os.path.join(root, name)
            os.rename(folder_path, new_path)
            rename_folder(new_path)
def replace_xcodeproj_file(path):
    log_manager = get_log_file_manager()
    for root, dirs, files in os.walk(path, topdown=True):
        dirs[:] = [d for d in dirs if d==(oc_util.name_current_project+'.xcodeproj')]
        for name in files:
            if name == 'project.pbxproj':
                file_path = os.path.join(root, name)
                for index in range(len(oc_util.class_folder.old_dir_list)):
                    old_text = oc_util.class_folder.old_dir_list[index]
                    new_text = oc_util.class_folder.new_dir_list[index]
                    log_manager.writelines('原文件夹名: %s 被修改为: %s\n'%(old_text, new_text))
                    replace_content(file_path, " " + old_text + " ", " " + new_text + " ")
                    replace_content(file_path, " " + old_text + ";", " " + new_text + ";")
                    replace_content(file_path, "/" + old_text + "/", "/" + new_text + "/")
                    replace_content(file_path, "/" + old_text + "\"", "/" + new_text + "\"")
                    replace_content(file_path, "\"" + old_text + "\"", "\"" + new_text + "\"")
                    replace_content(file_path, oc_util.name_current_project + '/' + old_text, oc_util.name_current_project + '/' + new_text)
def replace_content(path, old_text, new_text):
    with open(path, "r") as f:
        file_data = f.read()
        file_data = file_data.replace(old_text, new_text)
    with open(path, "w") as f:
        f.write(file_data)
def isTrue(d):
    my_list = ['.framework', '.lproj', '.bundle', '.Bundle', '.xcassets', '.appiconset', '.xcworkspace', '.xcodeproj']
    d_list = os.path.splitext(d)
    a1 = True if d_list[1] not in my_list else False
    a2 = (d!=(oc_util.name_current_project+'.xcodeproj'))
    a3 = d not in list(oc_yaml.list_folder_ignore_all)
    return a1 and a2 and a3
def get_log_file_manager():
    return open(oc_util.path_mix + '/文件夹混淆日志.txt', 'w')
def random_one_new_folder_name(old_name):
    ''' 随机一个文件夹名 '''
    if old_name in oc_util.class_folder.old_dir_list:
        index = oc_util.class_folder.old_dir_list.index(old_name)
        return oc_util.class_folder.new_dir_list[index]
    new_name = oc_util.prefix_folder
    keep_word = ''
    for word in list(oc_yaml.list_keep_word_for_folder):
        if old_name.endswith(word):
            keep_word = word
            break
    count = len(old_name) - len(keep_word) - len(oc_util.prefix_folder)
    new_name = new_name + random_name(count) + keep_word
    if new_name in oc_util.class_folder.new_dir_list:
        return random_one_new_folder_name(old_name)
    oc_util.class_folder.new_dir_list.append(new_name)
    oc_util.class_folder.old_dir_list.append(old_name)
    return new_name
def random_name(num):
    if num <= 3:
        num_list = ['3']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 4:
        num_list = ['4']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 5:
        num_list = ['5']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 6:
        num_list = ['6','33']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 7:
        num_list = ['7','34','43']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 8:
        # (8)(3,5)(5,3)(4,4)
        num_list = ['8','53','35','44']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 9:
        # (9)(6,3)(3,6)(5,4)(4,5)
        num_list = ['9','63','36','54','45']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 10:
        num_list = ['73','37','46','64','55','433','343','334']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 11: 
        num_list = ['83','38','47','74','56','65','335','353','533','443','434','344']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 12:
        num_list = ['93','39','84','48','75','57','66','336','363','633','345','354','543','534','435','453']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 13:
        num_list = ['94','49','85','58','67','76','445','454','544','355','535','553','337','373','3433','4333','3343','3334']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num == 14:
        num_list = ['95','59','68','86','77','455','545','554','446','464','644','3344','3434','3443','4433','4343','4334','3335','3353','3533','5333']
        num_string = random.choice(num_list)
        return get_unique_string(num_string)
    elif num>=15 and num<20:
        num_string = ''
        aList = ['3','4','5','6','7','8','9']
        for _ in range(5):
            num_string = num_string + random.choice(aList)
        return get_unique_string(num_string)
    elif num>=20 and num<30:
        num_string = ''
        aList = ['3','4','5','6','7','8','9']
        for _ in range(6):
            num_string = num_string + random.choice(aList)
        return get_unique_string(num_string)
    elif num>=30 and num<40:
        num_string = ''
        aList = ['3','4','5','6','7','8','9']
        for _ in range(7):
            num_string = num_string + random.choice(aList)
        return get_unique_string(num_string)
    elif num>=40 and num<50:
        num_string = ''
        aList = ['3','4','5','6','7','8','9']
        for _ in range(8):
            num_string = num_string + random.choice(aList)
        return get_unique_string(num_string)
    else:
        num_string = ''
        aList = ['3','4','5','6','7','8','9']
        for _ in range(9,12):
            num_string = num_string + random.choice(aList)
        return get_unique_string(num_string)

def get_unique_string(num_string):
    name = ''
    for num in num_string:
        if num == '3':
            name = name + oc_util.three().capitalize()
        elif num == '4':
            name = name + oc_util.four().capitalize()
        elif num == '5':
            name = name + oc_util.five().capitalize()
        elif num == '6':
            name = name + oc_util.six().capitalize()
        elif num == '7':
            name = name + oc_util.seven().capitalize()
        elif num == '8':
            name = name + oc_util.eight().capitalize()
        elif num == '9':
            name = name + oc_util.nine().capitalize()
    return name
