"""
修改图片名和图片hash、md5,需要指定图片文件夹名
"""
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml
from ObjectiveC import oc_tool
import os, re

def init():
    global file_mix_log
    file_mix_log = open(oc_util.path_mix + "/File混淆日志" + ".txt", "a")
    global old_image_name_list
    old_image_name_list = []
    global new_image_name_list
    new_image_name_list = []

    path = oc_util.path_mix_project
    rename_file_and_content_image_name(path)
    replace_file_content(path)
def rename_file_and_content_image_name(path):
    ''' 修改图片名 '''
    for root, dirs, _ in os.walk(path, topdown=True):
        for name in dirs:

            # 如果文件不存在，使用oc_util.name_current_project+'.bundle'
            if name not in oc_yaml.list_image_folder:
                name = oc_util.name_current_project+'.bundle'
                image_path = os.path.join(root, name)
                get_image_name(image_path)
            # 如果文件存在，使用文件名称
            else:
                image_path = os.path.join(root, name)
                get_image_name(image_path)

def replace_file_content(path):
    for root, _, files in os.walk(path, topdown=True):
        files[:] = [f for f in files if os.path.splitext(f)[1] in oc_yaml.list_support_open_file_type]
        for name in files:
            file_path = os.path.join(root, name)
            for index in range(len(old_image_name_list)):
                old_text = old_image_name_list[index]
                new_text = new_image_name_list[index]
                with open(file_path, 'r') as f:
                    file_content = f.read()
                    if old_text in file_content:
                        file_content = replace_word_in_file_content(file_content, old_text, new_text)
                with open(file_path, 'w') as f:
                    f.write(file_content)
def replace_word_in_file_content(file_content, old_word, new_word):
    p1 = re.compile(r'["]%s[".]'%old_word)
    aList = p1.findall(file_content)
    for a in aList:
        b = a[:1] + new_word + a[-1:]
        file_content = file_content.replace(a, b)
    return file_content
def get_image_name(image_path):
    for root, _, files in os.walk(image_path, topdown=True):
        for name in files:
            name_list = os.path.splitext(name)
            if name_list[1] in oc_yaml.list_image_type:
                top_name = name_list[0] # accountBindBgImg@2x
                if top_name.endswith('@2x'):
                    top_name = top_name.replace('@2x','')
                if top_name.endswith('@3x'):
                    top_name = top_name.replace('@3x','')
                new_name = random_one_new_image_name(top_name)
                if len(new_name)==0:
                    continue
                old_path = os.path.join(root, name)
                new_name = name.replace(top_name, new_name)
                new_path = os.path.join(root, new_name)
                file_mix_log.write('原图片名:%s 被修改为:%s\n'%(name.ljust(30), new_name))
                os.rename(old_path, new_path)
def random_one_new_image_name(old_name):
    ''' 随机一个图片名 '''
    if old_name in old_image_name_list:
        index = old_image_name_list.index(old_name)
        return new_image_name_list[index]
    if oc_tool.is_word_can_mix('图片', old_name) == False:
        return ''
    new_name = ''
    keep_word = ''
    for word in oc_yaml.list_keep_word:
        if old_name.endswith(word):
            keep_word = word
            break
    count = len(old_name) - len(new_name) - len(keep_word)
    new_name = new_name + oc_tool.random_one_string(count, False) + keep_word
    if oc_tool.is_name_legal(new_name) == False:
        return random_one_new_image_name(old_name)
    old_image_name_list.append(old_name)
    new_image_name_list.append(new_name)
    oc_util.class_oc_file.total_words.append(old_name)
    oc_util.class_oc_file.total_words.append(new_name)
    return new_name