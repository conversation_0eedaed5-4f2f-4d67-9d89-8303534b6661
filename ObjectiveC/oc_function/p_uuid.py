"""
修改xcodeproj中的uuid
"""
from ObjectiveC import oc_util
import os, re, random

def init():
    xcodeproj_name = oc_util.name_current_project + '.xcodeproj'
    modify_proj_uuid(oc_util.path_mix_project, xcodeproj_name)

def modify_proj_uuid(path, dir_name):
    for root, dirs, files in os.walk(path, topdown=True):
        dirs[:] = [d for d in dirs if d==dir_name]
        for name in files:
            if name == 'project.pbxproj':
                file_path = os.path.join(root, name)
                with open(file_path, "r") as f:
                    fileContent = f.read()
                    fileContent = search_and_replace_file_address(fileContent)
                with open(file_path, 'w') as f:
                    f.write(fileContent)

def search_and_replace_file_address(file_content):
    char_list = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F']
    old_uuid_list = []
    new_uuid_list = []
    aList = re.split(r'([\n \tG-Zg-z])', file_content)
    aList = [a for a in aList if (len(a.strip())!=0)]
    for a in aList:
        if len(a) == 24:
            if a not in old_uuid_list:
                old_uuid_list.append(a)
    # 生成新的uuid 并替换
    for uuid in old_uuid_list:
        tuple_new = random_new_uuid(new_uuid_list, char_list)
        new_uuid_list = tuple_new[1]
        new_uuid = tuple_new[0]
        file_content = file_content.replace(uuid, new_uuid) 
    return file_content

def hex_to_int(num):
    return int(num, 16)
def int_to_hex(num):
    return hex(num)[2:]

def random_new_uuid(new_uuid_list, char_list):
    new_uuid = ''
    for _ in range(24):
        new_uuid = new_uuid + random.choice(char_list)
    if new_uuid in new_uuid_list:
        return random_new_uuid(new_uuid_list, char_list)
    new_uuid_list.append(new_uuid)
    return (new_uuid, new_uuid_list)