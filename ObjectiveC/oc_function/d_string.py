"""
获取所有字符串并得到分割后的关键词
"""
import os, re
from ObjectiveC import oc_util
from ObjectiveC.oc_function import e_print_annotation

def get_project_string(save_path):
    list_string = []
    support_type = ['.h', '.m', '.mm']
    for root, dirs, files in os.walk(oc_util.path_mix_project, topdown=True):
        dirs[:] = [ d for d in dirs if d.endswith('.framework')==False]
        files[:] = [f for f in files if os.path.splitext(f)[1] in support_type]
        for name in files:
            file_path = os.path.join(root, name)
            with open(file_path, 'r') as f:
                file_content = f.read()
                search_string(file_content, list_string)
    list_words = []
    for each in list_string:
        a_list = re.split(r'[^a-zA-Z0-9_]', each)
        for a in a_list:
            if (len(a.strip())==0) or (is_number(a)) or (contain_english(a)==False) or (a in list_words): 
                continue
            list_words.append(a)
    # 排序
    list_words.sort(key=lambda i:len(i), reverse=True)
    # 写入文件
    file_manager = open(save_path, 'a+')
    for word in list_words:
        file_manager.write(word+'\n')
    file_manager.close()
# 是否为数字，含浮点数          
def is_number(num):
    pattern = re.compile(r'^[-+]?[-0-9]\d*\.\d*|[-+]?\.?[0-9]\d*$')
    result = pattern.match(num)
    if result:
        return True
    else:
        return False
# 是否包含英文字母
def contain_english(str0):
    return bool(re.search('[a-zA-Z]', str0))
def search_string(file_content, list_string):
    ''' 第二种查找字符串的方法 '''
    file_manager = open(oc_util.path_mix + "/工程中所有字符串列表" + ".txt", "a+")
    # 去注释和打印
    file_content = e_print_annotation.search_annotation_and_delete(file_content)
    file_content = e_print_annotation.search_print_and_delete(file_content)
    # 去空字符串
    file_content = file_content.replace('@""','')

    # 查找常量字符串并不计入(工程中字符串列表)中 
    const_manager = open(oc_util.path_mix + "/工程中常量字符串列表" + ".txt", "a+")
    # NSString *const _YYWebImageFadeAnimationKey = @"";
    p2 = re.compile(r'(NSString)( {0,})(\*)( {0,})(const)( {1,})(.*?)( {0,})(=)( {0,})(@")(.*?)([^\\])(")(;)')
    b_list = p2.findall(file_content)
    for b in  b_list:
        # static_list.append(''.join(b[7:-2]))
        my_string = ''.join(b[11:-2])
        const_manager.write(my_string+'\n')
    # static
    p2 = re.compile(r'(static)( {1,})(NSString)( {0,})(\*)( {0,})(.*?)( {0,})(=)( {0,})(@")(.*?)([^\\])(")(;)')
    b_list = p2.findall(file_content)
    for b in  b_list:
        my_string = ''.join(b[11:-2])
        const_manager.write(my_string+'\n')
    # NSLocalizedStringFromTable(@"Expected URL to be a file URL", @"AFNetworking", nil)
    p2 = re.compile(r'(NSLocalizedStringFromTable\(@")(.*?)(nil\))')
    # (@")(.*?)([^\\])(",)(@")(.*?)([^\\])(",nil)
    b_list = p2.findall(file_content)
    for b in  b_list:
        b_str = ''.join(b)
        p3 = re.compile(r'(@")(.*?)([^\\])(")')
        c_list = p3.findall(b_str)
        for c in c_list:
            my_string = ''.join(c[1] + c[2])
            const_manager.write(my_string+'\n')

    # (@"" )(@""])(@"";)(@"",)
    p1 = re.compile(r'(@")(.*?)([^\\])(")([ |\]|;|,|\n|:|\)|\.|\}|\?])', re.S)
    a_list = p1.findall(file_content)
    for a in a_list:
        str_a = ''.join(a[:-1])
        if str_a not in list_string:
            my_string = ''.join(a[1:-2])
            file_manager.write(my_string+'\n')
            list_string.append(str_a)

if __name__ == '__main__':
    string = '''
    "svdav" @"{mode_id:113,template_id:77}" "avsveafvea"
    @"vfsdavds" "fqevfew" "gsdbgs"
    @"e132"
    "4115"
    '''
    # string = string.replace('\\"', '陈')
    p1 = re.compile(r'(@")([^@"]*)(")')
    a_list = p1.findall(string)
    for a in a_list:
        print(''.join(a))
        print('------------------')