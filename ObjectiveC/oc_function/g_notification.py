"""
修改通知名
1、手动添加通知名到配置文件
2、将通知名改为如下这种形式(四九_xxxxxx_四九)
"""
import os, random, re
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml

def modify_project_notification():
    path = oc_util.path_mix_project
    global old_notify_name_list
    old_notify_name_list = []
    global new_notify_name_list
    new_notify_name_list = []
    for name in oc_yaml.list_need_change_notification_name:
        random_one_notification_name(name)
    for root, _, files in os.walk(path, topdown=True):
        files[:] = [f for f in files if os.path.splitext(f)[1] in ['.h', '.m', '.mm']]
        for name in files:
            file_path = os.path.join(root, name)
            with open(file_path, 'r') as f:
                file_content = f.read()
                file_content = search_and_replace_notification(file_content)
            with open(file_path, 'w') as f:
                f.write(file_content)
def search_and_replace_notification(file_content):
    p1 = re.compile(r'(@")(四九_)(.*?)(_四九)(")')
    a_list = p1.findall(file_content)
    for a in a_list:
        str_a = ''.join(a[1:-1])
        new_a = random_one_notification_name(str_a)
        old_name = ''.join(a)
        new_name = old_name.replace(str_a, new_a, 1)
        file_content = file_content.replace(old_name, new_name)
    return file_content
def random_one_notification_name(old_name):
    if old_name in old_notify_name_list:
        index = old_notify_name_list.index(old_name)
        return new_notify_name_list[index]
    new_name = 'kNotification'
    for _ in range(random.randint(1,4)):
        new_name = new_name + random.choice(oc_util.list_random_words).capitalize()
    # 防止通知名重复
    if new_name in new_notify_name_list:
        return random_one_notification_name(old_name)
    old_notify_name_list.append(old_name)
    new_notify_name_list.append(new_name)
    # 添加到总表中
    oc_util.class_oc_file.total_words.append(old_name)
    oc_util.class_oc_file.total_words.append(new_name)
    return new_name