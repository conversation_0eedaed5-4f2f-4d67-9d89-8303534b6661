"""
字符串加密和分割
"""
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml
from ObjectiveC import oc_tool, oc_encode_decode
import os, random, time

def init():
    path = oc_util.path_mix_project

    # 待分割的字符串数组
    global all_string_list
    all_string_list = oc_util.get_text_lines(oc_util.path_mix + "/工程中所有字符串列表" + ".txt")
    all_string_list[:] = [a for a in all_string_list if len(a)>1]

    global const_string_list
    const_string_list = oc_util.get_text_lines(oc_util.path_mix + "/工程中常量字符串列表" + ".txt")
    const_string_list.append(r'GET')
    const_string_list.append(r'HEAD')

    # 拆分字符串
    # breakup_search_and_replace(path)
    # base64 无key 加密
    base64_encode_and_replace(path)
def base64_encode_and_replace(path):
    # 新建解密文件和所在文件夹
    new_file_name = ''
    for _ in range(1,2):
        new_file_name = new_file_name + random.choice(oc_util.list_random_words).capitalize()
    new_folder_name = random.choice(oc_util.list_random_words).capitalize() + 'Extension'
    folder_path = path + '/' + oc_util.name_current_project + '/' + new_folder_name
    os.mkdir(folder_path)
    header_file_name = 'NSString+%s'%new_file_name + '.h'
    method_file_name = 'NSString+%s'%new_file_name + '.m'
    header_file_path = folder_path + '/' + header_file_name
    method_file_path = folder_path + '/' + method_file_name
    new_method_name = random.choice(oc_util.list_random_words)
    for _ in range(random.randint(1,3)):
        new_method_name = new_method_name + random.choice(oc_util.list_random_words).capitalize()
    with open(header_file_path, 'a+') as f:
        file_content = f.read()
        file_content = write_header_file(file_content, new_file_name, new_method_name)
    with open(header_file_path, 'w') as f:
        f.write(file_content)

    with open(method_file_path, 'a+') as f:
        file_content = f.read()
        file_content = write_method_file(file_content, new_file_name, new_method_name)
    with open(method_file_path, 'w') as f:
        f.write(file_content)
    # 添加新建文件夹和文件的引用
    oc_tool.add_group(new_folder_name,oc_util.name_current_project)
    # 文件存放路径
    f_path = oc_util.path_mix_project + '/' + oc_util.name_current_project + '/' + new_folder_name + '/'
    oc_tool.add_file(new_folder_name, f_path + header_file_name)
    oc_tool.add_file(new_folder_name, f_path + method_file_name)

    # 哈哈
    for root, dirs, files in os.walk(path, topdown=True):
        dirs[:] = [ d for d in dirs if d.endswith('.framework')==False]
        files[:] = [f for f in files if os.path.splitext(f)[1] in oc_yaml.list_support_open_file_type]
        for name in files:
            file_path = os.path.join(root, name)

            with open(file_path, 'r') as f:
                file_content = f.read()
                for old in all_string_list:
                    if old in const_string_list:
                        continue
                    oc_old = '@"' + old + '"'
                    if oc_old in file_content:
                        en_string = oc_encode_decode.hs_base64_encode(old)
                        add_content = '\n#import "NSString+%s.h"\n\n'%new_file_name
                        if add_content not in file_content:
                            file_content =  add_content + file_content
                        file_content = file_content.replace(oc_old, '@"' + en_string + '"' + '.' + new_method_name)
            with open(file_path, 'w') as f:
                f.write(file_content)

def breakup_search_and_replace(path):
    for root, dirs, files in os.walk(path, topdown=True):
        dirs[:] = [ d for d in dirs if d.endswith('.framework')==False]
        files[:] = [f for f in files if os.path.splitext(f)[1] in oc_yaml.list_support_open_file_type]
        for name in files:
            file_path = os.path.join(root, name)
            with open(file_path, 'r') as f:
                file_content = f.read()
                for old in all_string_list:
                    if old in const_string_list:
                        continue
                    oc_old = '@"' + old + '"'
                    if oc_old in file_content:
                        file_content = one_string_method(file_content, old)
            with open(file_path, 'w') as f:
                f.write(file_content)
def write_header_file(file_content, file_name, method_name):
    file_content = file_content + '\n#import <Foundation/Foundation.h>\n\n\n\n'
    file_content = file_content + 'NS_ASSUME_NONNULL_BEGIN\n\n'
    file_content = file_content + '@interface NSString(%s)\n\n'%file_name
    file_content = file_content + '- (NSString *)%s;\n\n'%method_name
    file_content = file_content + '@end\n\n'
    file_content = file_content + 'NS_ASSUME_NONNULL_END\n\n'
    return file_content

def write_method_file(file_content, file_name, method_name):
    file_content = file_content + '\n#import "NSString+%s.h"\n\n'%file_name
    file_content = file_content + '@implementation NSString(%s)\n\n'%file_name
    file_content = file_content + '- (NSString *)%s\n'%method_name + '{\n'
    file_content = file_content + '    NSData *data = [[NSData alloc] initWithBase64EncodedString:self options:0];\n'
    file_content = file_content + '    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];\n' + '}\n\n'
    file_content = file_content + '@end\n'
    return file_content
def one_string_method(file_content, old_string):
    # NSArray * array = @[@"a",@"b",@"c",@"d"];
    # NSString *string = [array componentsJoinedByString:@""];
    x = ''
    for a in old_string:
        symbol = ','
        if len(x) == 0:
            symbol = ''
        if a == '\\':
            a = '\\\\'
        if a == '"':
            a = '\\"'
        x = x + symbol +  '@' + '"' + a + '"'
    x = '@[' + x + ']'
    one_string = '[%s componentsJoinedByString:@""]' % x
    old_string = '@"' + old_string + '"'
    file_content = file_content.replace(old_string, one_string)
    return file_content

one_string_method('1', 'hainanjunbigao.com')


