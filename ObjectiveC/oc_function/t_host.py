"""
更换域名
"""
import os
from ObjectiveC import oc_util, oc_yaml

def init():
    my_tuple = return_old_list_and_new()
    old_list = my_tuple[0]
    new_list = my_tuple[1]

    for root, dirs, files in os.walk(oc_util.path_mix_project,topdown=True):
        dirs[:] = [d for d in dirs if isTrue(d)]
        files[:] = [f for f in files if (os.path.splitext(f)[1] in oc_yaml.list_support_open_file_type)]
        for name in files:
            file_path = os.path.join(root, name)
            with open(file_path, 'r') as f:
                file_content = f.read()
                for index in range(len(old_list)):
                    old = old_list[index]
                    new = new_list[index]
                    if old in file_content:
                        file_content = file_content.replace(old, new)
            with open(file_path, 'w') as f:
                f.write(file_content)

def return_old_list_and_new():
    old_list = []
    new_list = []
    old_list.append('@"guangzhouxingwan.com"')
    new_list.append('@"sfengchang.com"')

    old_list.append('@"https://api.nsdk."')
    new_list.append('@"https://tag.item."')

    old_list.append('@"https://pay.nsdk."')
    new_list.append('@"https://box.item."')

    old_list.append('@"FNSDKHTTPRandomAPI"')
    new_list.append('@"manager"')

    old_list.append('@"/sdk/init/index"')
    new_list.append('@"/one_two_setting"')

    old_list.append('@"/accounts/users/register"')
    new_list.append('@"/elements_level_lazinessPivot"')

    old_list.append('@"/accounts/users/checkRegister"')
    new_list.append('@"/elements_level_ballerinaInitiation"')

    old_list.append('@"/accounts/users/logon?"')
    new_list.append('@"/elements_level_interweaveSewer"')

    old_list.append('@"/accounts/users/getcode"')
    new_list.append('@"/elements_level_logisticPurple"')

    old_list.append('@"/accounts/users/checkPhoneRegister"')
    new_list.append('@"/elements_level_barbScrew"')

    old_list.append('@"/accounts/users/phoneregister"')
    new_list.append('@"/elements_level_powerlessnessAmbience"')

    old_list.append('@"/accounts/users/randregister"')
    new_list.append('@"/elements_level_baseSecure"')

    old_list.append('@"/accounts/users/answerchangepassword"')
    new_list.append('@"/elements_level_staffCook"')

    old_list.append('@"/accounts/users/securityquestionister"')
    new_list.append('@"/elements_level_trophyResurface"')

    old_list.append('@"/accounts/users/getcodeboundphone"')
    new_list.append('@"/elements_level_heartReligion"')

    old_list.append('@"/accounts/users/boundphone"')
    new_list.append('@"/elements_level_litmusHypothesis"')

    old_list.append('@"/accounts/users/idcardvalidation"')
    new_list.append('@"/elements_level_acclaimedRobotic"')

    old_list.append('@"/games/games/collectuserext?"')
    new_list.append('@"/large_large_saveShameful"')

    old_list.append('@"/accounts/users/resetpasswordsms"')
    new_list.append('@"/elements_level_estrogenOtter"')

    old_list.append('@"/accounts/users/newsdkphoneresetpassword"')
    new_list.append('@"/elements_level_thrillTowards"')

    old_list.append('@"/pay/Pay/PaySelfms"')
    new_list.append('@"/map_Map_Strongest"')

    old_list.append('@"/pay/Pay/GetConfig"')
    new_list.append('@"/map_Map_Optional"')

    old_list.append('@"/pay/pay/unifypay"')
    new_list.append('@"/map_map_extending"')

    old_list.append('@"/pay/PayBack/callback_notify"')
    new_list.append('@"/min_packet_retrainingAdmirer"')

    return (old_list, new_list)
def isTrue(d):
    return (d.endswith('.xcodeproj')==False) and (d.endswith('.framework')==False)
