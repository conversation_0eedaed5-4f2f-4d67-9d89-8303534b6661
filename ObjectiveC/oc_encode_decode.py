import base64

def hs_base64_encode(my_string):
    ''' base64加密 '''
    bytes_name = my_string.encode('utf-8')
    str_name = base64.b64encode(bytes_name)
    return str(str_name, encoding='utf-8')
def hs_base64_decode(my_string):
    ''' base64解密 '''
    bytes_string = bytes(my_string, 'utf-8')
    return base64.b64decode(bytes_string).decode('utf-8')


if __name__ == '__main__':
    x = hs_base64_encode('chen')
    print('加密前:', 'chen')
    print('加密后:', x)
    print('解密后:', hs_base64_decode(x))