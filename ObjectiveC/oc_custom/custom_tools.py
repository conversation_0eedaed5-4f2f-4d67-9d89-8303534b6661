"""
通用工具函数模块，包含加密解密等功能
"""
import random
import hashlib
import binascii

def encrypt_with_random_iv(plain_data):
    """
    使用随机IV加密数据，返回二进制数据
    参数:
        plain_data: 二进制数据
    返回:
        加密后的二进制数据，前16字节为IV
    """
    if not plain_data:
        return b""
    
    # 生成16字节随机IV
    iv = bytes([random.randint(0, 255) for _ in range(16)])
    
    # 用IV循环异或明文
    encrypted_data = bytearray()
    for i, byte in enumerate(plain_data):
        cipher_byte = byte ^ iv[i % 16]
        encrypted_data.append(cipher_byte)
    
    # 返回IV + 加密数据
    return iv + encrypted_data

def decrypt_with_random_iv(encrypted_data):
    """
    解密：从前16字节取IV，再滚动异或还原明文
    参数:
        encrypted_data: 加密后的二进制数据，前16字节为IV
    返回:
        解密后的二进制数据
    """
    if len(encrypted_data) < 16:
        return b""
    
    # 提取IV（前16字节）
    iv = encrypted_data[:16]
    
    # 解密数据（从第17字节开始）
    cipher_data = encrypted_data[16:]
    plain_data = bytearray()
    
    for i, byte in enumerate(cipher_data):
        plain_byte = byte ^ iv[i % 16]
        plain_data.append(plain_byte)
    
    return bytes(plain_data)

def calculate_file_md5(file_path):
    """
    计算文件的 MD5 哈希值
    """
    md5_hash = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            # 分块读取文件以处理大文件
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()
    except Exception as e:
        print(f"计算文件 {file_path} 的 MD5 失败: {e}")
        return "计算失败"

# 以下是辅助函数，用于在需要时进行十六进制转换
def data_to_hex(data):
    """将二进制数据转换为十六进制字符串"""
    return binascii.hexlify(data).decode('ascii').upper()

def hex_to_data(hex_string):
    """将十六进制字符串转换为二进制数据"""
    return binascii.unhexlify(hex_string)