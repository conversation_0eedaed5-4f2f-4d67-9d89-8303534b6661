"""
修改指定工程名
"""
import os, json, random, re, plistlib
import sys
from ObjectiveC import oc_util
from ObjectiveC.oc_function.b_project import (
    replace_project_name_in_all_files,
    replace_xcode_project_reference,
    replace_import_statements,
    replace_objc_string_literals,
    replace_file_content,
    random_one_project_name
)

def find_project_by_name(search_dir, project_name):
    """
    在指定目录中查找指定名称的Xcode项目
    
    参数:
        search_dir (str): 要搜索的目录路径
        project_name (str): 要查找的项目名称（不包含.xcodeproj扩展名）
        
    返回:
        str: 找到的项目路径，如果未找到则返回None
    """
    project_dir_name = f"{project_name}.xcodeproj"
    
    print(f"开始在 {search_dir} 中查找项目: {project_dir_name}")
    
    for root, dirs, _ in os.walk(search_dir, topdown=True):
        if project_dir_name in dirs:
            project_path = os.path.join(root, project_dir_name)
            project_parent_dir = root
            print(f"找到项目: {project_path}")
            return project_parent_dir
    
    print(f"未找到名为 {project_name} 的项目")
    return None

def modify_dir_name(project_dir, old_name, new_name):
    """
    修改项目目录名称
    """
    contains_dir = [old_name, old_name+'.xcodeproj']
    for root, dirs, _ in os.walk(project_dir, topdown=True):
        dirs[:] = [d for d in dirs if d in contains_dir]
        for name in dirs:
            dir_path = os.path.join(root, name)
            name = name.replace(old_name, new_name, 1)
            new_path = os.path.join(root, name)
            os.rename(dir_path, new_path)
            print(f"已重命名目录: {dir_path} -> {new_path}")

def modify_file_content(project_dir, old_name, new_name):
    """
    修改项目文件内容
    """
    contains_file = ['project.pbxproj', old_name+'.xcscheme', old_name+'.h', old_name+'.m']
    for root, _, files in os.walk(project_dir, topdown=True):
        files[:] = [f for f in files if f in contains_file]
        for name in files:
            file_path = os.path.join(root, name)
            with open(file_path, 'r') as f:
                file_content = f.read()
                file_content = replace_file_content(file_content, old_name, new_name)

                if name == (old_name+'.h') or name == (old_name+'.m'):
                    file_content = replace_file_content(file_content, old_name+'VersionNumber;', new_name+'VersionNumber;')
                    file_content = replace_file_content(file_content, old_name+'VersionString', new_name+'VersionString')
                    file_content = replace_file_content(file_content, 'path ='+old_name+'/', 'path ='+new_name+'/')
                    file_content = replace_objc_string_literals(file_content, old_name, new_name)
            with open(file_path, 'w') as f:
                f.write(file_content)
            
            # 重命名文件
            if old_name in name:
                new_file_name = name.replace(old_name, new_name)
                new_path = os.path.join(root, new_file_name)
                os.rename(file_path, new_path)
                print(f"已重命名文件: {file_path} -> {new_path}")

def modify_mac_login_name(project_dir, old_text, new_text):
    """
    修改mac登录用户名
    """
    op_path = ''
    for root, dirs, _ in os.walk(project_dir, topdown=True):
        dirs[:] = [d for d in dirs if d==(old_text+'.xcodeproj')]
        for name in dirs:
            op_path = os.path.join(root, name)
    
    if not op_path:
        print(f"未找到 {old_text}.xcodeproj 目录")
        return
        
    for root, dirs, files in os.walk(op_path, topdown=True):
        for name in dirs:
            name_tuple = os.path.splitext(name)
            if name_tuple[1] == '.xcuserdatad':
                old_path = os.path.join(root, name)
                new_name = random.choice(oc_util.list_random_words).lower()
                name = name.replace(name_tuple[0], new_name)
                new_path = os.path.join(root, name)
                os.rename(old_path, new_path)
                print(f"已修改用户数据目录: {old_path} -> {new_path}")
    
    for root, dirs, files in os.walk(op_path, topdown=True):
        for name in files:
            if name == 'xcschememanagement.plist':
                file_path = os.path.join(root, name)
                with open(file_path, 'rb') as f:
                    file_content = plistlib.load(f)
                    name_dict = file_content['SchemeUserState']
                    old_key = '%s.xcscheme_^#shared#^_'%old_text
                    if old_key in name_dict:
                        new_key = '%s.xcscheme_^#shared#^_'%new_text
                        name_dict[new_key] = name_dict.pop(old_key)
                        file_content['SchemeUserState'] = name_dict
                        with open(file_path, 'wb') as f:
                            plistlib.dump(file_content, f)
                        print(f"已更新 xcschememanagement.plist 中的方案引用")

def custom_modify_project_name(search_dir, old_name, new_name=None):
    """
    修改指定工程名

    参数:
        search_dir (str): 要搜索的目录路径
        old_name (str): 要修改的项目名称
        new_name (str, optional): 新的项目名称，如果为None则随机生成

    返回:
        str: 操作成功返回新的项目名称，失败返回None

    使用示例:
        new_name = custom_modify_project_name('/path/to/search', 'OldProjectName', 'NewProjectName')
    """
    # 查找项目
    project_dir = find_project_by_name(search_dir, old_name)
    if not project_dir:
        print(f"错误: 未找到名为 {old_name} 的项目")
        return None
    
    # 如果未指定新名称，则随机生成
    if new_name is None:
        new_name = random_one_project_name()
    
    print(f"开始修改项目名称: {old_name} -> {new_name}")
    
    # 保存当前的 path_mix_project 值
    original_path_mix_project = oc_util.path_mix_project
    
    try:
        # 临时设置 path_mix_project 为找到的项目目录
        oc_util.path_mix_project = project_dir
        
        # 修改目录名
        modify_dir_name(project_dir, old_name, new_name)
        
        # 修改文件内容
        modify_file_content(project_dir, old_name, new_name)
        
        # 修改mac登录名
        modify_mac_login_name(project_dir, old_name, new_name)
        
        # 替换所有文件中的工程名
        replace_project_name_in_all_files(old_name, new_name)
        
        print(f"项目名称修改完成: {old_name} -> {new_name}")
        return new_name

    except Exception as e:
        print(f"修改项目名称时出错: {e}")
        return None
        
    finally:
        # 恢复原始的 path_mix_project 值
        oc_util.path_mix_project = original_path_mix_project

# 使用示例
if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python custom_project.py <搜索目录> <旧项目名> [新项目名]")
        sys.exit(1)
    
    search_dir = sys.argv[1]
    old_name = sys.argv[2]
    new_name = sys.argv[3] if len(sys.argv) > 3 else None
    
    custom_modify_project_name(search_dir, old_name, new_name)
