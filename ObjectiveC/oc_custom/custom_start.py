from ObjectiveC.oc_function import b_project as project
from ObjectiveC import oc_util
from ObjectiveC.oc_custom import custom_pbxproj
from ObjectiveC.oc_custom import custom_file
from ObjectiveC.oc_custom import custom_replace
from ObjectiveC.oc_custom import custom_util

# 国内还是国外 1国内 2海外
def init():
    if oc_util.name_current_project != 'XXGPlayKit':
        return
    sdk_cn_or_os = custom_util.sdk_cn_or_os
    startid = custom_util.startid
    tmp_cn = 'XXGPlayKitCN'
    tmp_os = 'XXGPlayKitOS'
    del_target_name = tmp_os if sdk_cn_or_os == '1' else tmp_cn
    prj_name = tmp_cn if sdk_cn_or_os == '1' else tmp_os
    proj_path = f'{oc_util.path_mix_project}/{oc_util.name_current_project}.xcodeproj/project.pbxproj'
    
    # 删除target
    custom_pbxproj.delete_target_using_pbxproj(proj_path, del_target_name)

    # 修改工程名
    project.modify_project_name(prj_name)

    # 删除宏内容
    custom_file.delete_macro_content(prj_name)

    # 删除文件
    delete_folder()

    # 从 workspace 中移除工程
    remove_project_from_workspace()

    # 移除指定工程中指定库/文件
    remove_framework_from_demo_project()

    # 处理MiddleWare
    handle_middleware()
    
    # 如果startid不为空，则替换 shineupon_map.json 中的 xxpk_startid 为 startid
    shineupon_map_path = f'{oc_util.path_mix_project}/{oc_util.name_current_project}.bundle/xxpk_a_shineupon_map.json'
    if startid is not None and startid.strip() != '':
        custom_replace.replace_key_in_file(shineupon_map_path,'xxpk_startid', startid)

    # 从shineupon_map_path中读取 xxpk_version
    custom_util.sdk_version = custom_replace.get_key_value_from_file(shineupon_map_path,'xxpk_version')
    print(f'读取到的xxpk_version: {custom_util.sdk_version}')

# 根据sdk_cn_or_os 删除工程指定文件
def delete_folder():
    sdk_cn_or_os = custom_util.sdk_cn_or_os
    sdk_options = custom_util.sdk_options
    # 创建一个数组，存储要删除的文件夹路径
    folders_to_delete = []
    # 根据sdk_cn_or_os 删除工程指定文件
    if sdk_cn_or_os == '1':
        # 将要删除的文件夹路径添加到数组中
        folders_to_delete.append(f'{oc_util.path_mix_project}/OSFrameworks')
        folders_to_delete.append(f'{oc_util.path_mix_project}/OSResources')
        folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo')
    elif sdk_cn_or_os == '2':
        # 将要删除的文件夹路径添加到数组中
        folders_to_delete.append(f'{oc_util.path_mix_project}/CNFrameworks')
        folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitCNDemo')
        # 如果不需要Pods，则删除XXGPlayKitOSDemo-Pods文件夹
        if custom_util.need_pods == False:
            folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/Pods')
            folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/podfile')
            folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/Podfile.lock')
            folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/XXGPlayKitOSDemo-Pods.xcodeproj')
        else:
            folders_to_delete.append(f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/XXGPlayKitOSDemo.xcodeproj')
    # 循环遍历数组，删除文件夹
    for folder_path in folders_to_delete:
        custom_file.delete_file_or_folder(folder_path)

# 从 workspace 中移除工程
def remove_project_from_workspace():
    workspace_path = f'{oc_util.path_mix_project}/XXGPlayKit.xcworkspace'
    sdk_cn_or_os = custom_util.sdk_cn_or_os
    projects_to_remove = []
    if sdk_cn_or_os == '1':
        projects_to_remove.append('XXGPlayKitOSDemo')
        projects_to_remove.append('XXGPlayKitOSDemo-Pods')
        projects_to_remove.append('Pods')
    elif sdk_cn_or_os == '2':
        projects_to_remove.append('XXGPlayKitCNDemo')
        if custom_util.need_pods == False:
            projects_to_remove.append('XXGPlayKitOSDemo-Pods')
            projects_to_remove.append('Pods')
        else:
            projects_to_remove.append('XXGPlayKitOSDemo')

    # 循环遍历数组，删除文件夹
    for project_name in projects_to_remove:
        custom_file.remove_project_from_workspace(workspace_path, project_name)

# 移除指定工程中指定库/文件
def remove_framework_from_demo_project():
    sdk_cn_or_os = custom_util.sdk_cn_or_os
    sdk_options = custom_util.sdk_options
    demo_project_path = ''

    # 要删除的框架组列表
    framework_groups = []
    
    # 移除库 sdk_list = ['ShanYanSDK(一键登录)', 'BDASignalManager(巨量归因)', 'WechatOpenSDK(微信)', 'SigmobSDK(激励广告)']
    if sdk_cn_or_os == '1':
        demo_project_path = f'{oc_util.path_mix_project}/XXGPlayKitCNDemo/XXGPlayKitCNDemo.xcodeproj/project.pbxproj'

        if 'ShanYanSDK' not in sdk_options:
            framework_groups.append(['ShanYan', 'libXXGShanYanMiddleware.a', 'CNFrameworks/ShanYan'])
        if 'BDASignalManager' not in sdk_options:
            framework_groups.append(['BDASignal', 'libXXGBDASignalMiddleware.a', 'CNFrameworks/BDASignal'])
        if 'WechatOpenSDK' not in sdk_options:
            framework_groups.append(['WechatOpenSDK', 'libXXGWechatOpenSDKMiddleware.a', 'CNFrameworks/WechatOpenSDK'])
        if 'SigmobSDK' not in sdk_options:
            framework_groups.append(['SigmobSDK', 'libXXGSigmobSDKMiddleware.a', 'CNFrameworks/SigmobSDK'])

    # 移除库 sdk_list = ['AppsFlyer', 'Facebook', 'Firebase', 'Adjust', 'VK(俄罗斯VK登录)', 'AppLovin(Max广告)', 'Poopo(渠道)']
    elif sdk_cn_or_os == '2':
        demo_project_path = f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/XXGPlayKitOSDemo.xcodeproj/project.pbxproj'
        if custom_util.need_pods == True:
            demo_project_path = f'{oc_util.path_mix_project}/XXGPlayKitOSDemo/XXGPlayKitOSDemo-Pods.xcodeproj/project.pbxproj'

        if 'AppsFlyer' not in sdk_options:
            framework_groups.append([
                'AppsFlyer', 
                'libXXGAppFlyerMiddleware.a', 
                'OSFrameworks/AppsFlyer',
                'OSFrameworks/AppsFlyer/AppsFlyerLib.framework'
            ])
        if 'Facebook' not in sdk_options:
            framework_groups.append([
                'Facebook', 
                'libXXGFacebookMiddleware.a',
                'OSFrameworks/Facebook',
                'OSFrameworks/Facebook/FBSDKCoreKit.framework',
                'OSFrameworks/Facebook/FBSDKLoginKit.framework',
                'OSFrameworks/Facebook/FBSDKShareKit.framework'
            ])
        if 'Firebase' not in sdk_options:
            framework_groups.append([
                'Firebase', 
                'libXXGFirebaseMiddleware.a', 
                'OSResources/GoogleService-Info.plist',
                'OSFrameworks/Firebase',
                'OSFrameworks/Firebase/FirebaseAnalytics',
                'OSFrameworks/Firebase/FirebaseCrashlytics'
            ])
        if 'Adjust' not in sdk_options:
            framework_groups.append([
                'Adjust', 
                'libXXGAdjustMiddleware.a',
                'OSFrameworks/Adjust',
                'OSFrameworks/Adjust/Adjust.framework'
            ])
        if 'VK' not in sdk_options:
            framework_groups.append([
                'VK', 
                'libXXGVKMiddleware.a', 
                'OSResources/VKID-Core-Resources.bundle', 
                'OSResources/VKID-Resources.bundle',
                'OSFrameworks/VK',
                'OSFrameworks/VK/VKSDK.framework'
            ])
        if 'AppLovin' not in sdk_options:
            framework_groups.append([
                'AppLovin', 
                'libXXGAppLovinMiddleware.a',
                'OSFrameworks/AppLovin',
                'OSFrameworks/AppLovin/AppLovinSDK.framework'
            ])
        if 'Poopo' not in sdk_options:
            framework_groups.append([
                'Poopo', 
                'libXXGPoopoMiddleware.a',
                'OSResources/SSBundle.bundle',
                'OSFrameworks/Poopo',
                'OSFrameworks/Poopo/JYouLoginKit.framework'
            ])
    
    # 如果没有需要删除的框架，直接返回
    if not framework_groups:
        print("没有需要删除的框架")
        return
        
    # 单独处理每组框架文件
    for group in framework_groups:

        print(f"正在处理框架组: {group}")
        try:
            # 对每个框架组，调用remove_files_from_project函数单独处理
            custom_pbxproj.remove_files_from_project(demo_project_path, group)
        except Exception as e:
            print(f"处理框架组 {group} 时出错: {e}")
            continue

        # 如果文件夹中包含/则删除文件夹
        for item in group:
            if item.startswith('OSFrameworks/') or item.startswith('OSResources/') or item.startswith('CNFrameworks/') and '/' in item:
                folder_path = f'{oc_util.path_mix_project}/{item}'
                try:
                    print(f"尝试直接删除文件夹: {folder_path}")
                    custom_file.delete_file_or_folder(folder_path)
                except Exception as e:
                    print(f"删除文件夹失败: {e}")

# 处理MiddleWare
def handle_middleware():
    sdk_options = custom_util.sdk_options
    middleware_project_path = f'{oc_util.path_mix_project}/XXGThirdMiddlewares/XXGThirdMiddlewares.xcodeproj/project.pbxproj'
    middleware_delete_list = []
    if 'ShanYanSDK' not in sdk_options:
        middleware_delete_list.append('XXGShanYanMiddleware')
    if 'BDASignalManager' not in sdk_options:
        middleware_delete_list.append('XXGBDASignalMiddleware')
    if 'WechatOpenSDK' not in sdk_options:
        middleware_delete_list.append('XXGWechatOpenSDKMiddleware')
    if 'SigmobSDK' not in sdk_options:
        middleware_delete_list.append('XXGSigmobMiddleware')
        
    if 'AppsFlyer' not in sdk_options:
        middleware_delete_list.append('XXGAppFlyerMiddleware')
    if 'Facebook' not in sdk_options:
        middleware_delete_list.append('XXGFacebookMiddleware')
    if 'Firebase' not in sdk_options:
        middleware_delete_list.append('XXGFirebaseMiddleware')
    if 'Adjust' not in sdk_options:
        middleware_delete_list.append('XXGAdjustMiddleware')
    if 'VK' not in sdk_options:
        middleware_delete_list.append('XXGVKMiddleware')
    if 'AppLovin' not in sdk_options:
        middleware_delete_list.append('XXGAppLovinMiddleware')
    if 'Poopo' not in sdk_options:
        middleware_delete_list.append('XXGPoopoMiddleware')
        
    for middleware_name in middleware_delete_list:
        # 删除target
        custom_pbxproj.delete_target_using_pbxproj(middleware_project_path, middleware_name)
