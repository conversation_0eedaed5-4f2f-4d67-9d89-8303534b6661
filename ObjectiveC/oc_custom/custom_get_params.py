import random
import os
from ObjectiveC.oc_custom import custom_util
from ObjectiveC import oc_util

def get_params():

    # 国内还是国外 1国内 2海外 #########################################################
    while True:
        sdk_cn_or_os = input("请输入需要国内还是海外\n1.国内\n2.海外\n\n").strip()
        if sdk_cn_or_os == '':
            custom_util.sdk_cn_or_os = '2'
            sdk_cn_or_os = '2'
            break
        elif sdk_cn_or_os in ('1', '2'):
            custom_util.sdk_cn_or_os = sdk_cn_or_os
            break
        else:
            print("输入无效，请输入1或2")
    
    # 打印 oc_util.name_current_project
    print(f'当前工程名: {oc_util.name_current_project}')
    if oc_util.name_current_project != 'XXGPlayKit':
        return
    
    # 新的工程名
    new_project_name = input("请输入新的工程名(可为空)\n").strip()
    if new_project_name == '':
        name = ''
        for _ in range(random.randint(1, 1)):
            name = name + random.choice(oc_util.list_random_words).capitalize()
            name = name + random.choice(oc_util.list_random_words).capitalize()
        oc_util.new_project_name = name

    # 请输入初始化id #########################################################
    while True:
        startid = input("请输入初始化id(可为空)\n").strip()
        if startid == '':
            # 输入为空，赋值并跳出循环
            custom_util.startid = ''
            break
        elif len(startid) == 32:
            # 输入为32位，赋值并跳出循环
            custom_util.startid = startid
            break
        else:
            # 输入不为空且不是32位，提示错误
            print("输入无效，请输入32位的初始化id或留空")
    
    # 根据国内/海外选择添加SDK选项 #########################################################
    sdk_options = ""
    
    # 定义SDK列表和默认值
    if sdk_cn_or_os == '1':  # 国内
        sdk_list = ['ShanYanSDK(一键登录)', 'BDASignalManager(巨量归因)', 'WechatOpenSDK(微信)', 'SigmobSDK(激励广告)']
        default_sdk = 'ShanYanSDK,BDASignalManager'
    elif sdk_cn_or_os == '2':  # 海外
        sdk_list = ['AppsFlyer', 'Facebook', 'Firebase', 'Adjust', 'VK(俄罗斯VK登录)', 'AppLovin(Max广告)', 'Poopo(渠道)']
        default_sdk = 'AppsFlyer,Facebook,Firebase'
    
    # 生成选择提示
    sdk_options_prompt = "请选择需要包含的SDK（多选请用逗号分隔，如：0,1）\n"
    for i, sdk in enumerate(sdk_list):
        sdk_options_prompt += f"{i}.{sdk}\n"
    sdk_options_prompt += "\n"
    
    # 获取用户输入
    sdk_options = input(sdk_options_prompt).strip()
    
    # 处理用户输入
    if sdk_options == '':
        # 输入为空，使用默认值
        sdk_options = default_sdk
        print(f"默认选择: {sdk_options}")
    else:
        try:
            selected_indices = [int(idx.strip()) for idx in sdk_options.split(',')]
            selected_sdks = [sdk_list[idx] for idx in selected_indices if 0 <= idx < len(sdk_list)]
            if not selected_sdks:
                # 如果没有有效选择，使用默认值
                sdk_options = default_sdk
                print(f"无有效选择，默认选择: {sdk_options}")
            else:
                sdk_options = ','.join(selected_sdks)
                print(f"已选择: {sdk_options}")
        except (ValueError, IndexError):
            # 输入格式错误，使用默认值
            sdk_options = default_sdk
            print(f"输入格式有误，默认选择: {sdk_options}")

    custom_util.sdk_options = sdk_options

    # 检查是否包含AppLovin，如果包含则设置need_pods为True
    if 'AppLovin' in sdk_options:
        custom_util.need_pods = True
    else:
        custom_util.need_pods = False

