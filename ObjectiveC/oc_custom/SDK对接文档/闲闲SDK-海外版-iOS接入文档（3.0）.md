# 闲闲SDK-海外版-iOS接入文档
<center>当前版本 v3.2.0</center>

[toc]

> 🌍 闲闲SDK-海外版Server接入文档：[https://psm4i21lig.feishu.cn/docx/EEundeNEHoIm7Rx7IlkcoOyGnBf](https://psm4i21lig.feishu.cn/docx/EEundeNEHoIm7Rx7IlkcoOyGnBf)

## 更新日志

- v3.2.0：2025.5.15
  - 修复已知bug，完善功能

=== [基础篇] ===

## 环境支持

- iOS: `13.0` 及以上
- 架构: `arm64`
- Xcode:  `15.2` 及以上
  - 上传AppStoreConnect 所需 Xcode版本参考Apple官方文档 [https://developer.apple.com/ios/submit/](https://developer.apple.com/ios/submit/) `实时更新`

## 集成SDK

### 参数准备

集成所需参数如下：

- SDK App ID
  - appid  // 配置 `URL Scheme` 处使用

### 接入方式

#### 静态接入

>  【SDK】目前只支持静态库接入方式

将所有文件【SDK】添加到Xcode工程中，确保在工程的主目录下

#### 添加 swift文件

由于SDK使用到包含swift的第三方库，所以项目中需包含一个swift文件，达到重新检查并修复模块间的依赖关系

如果Xcode工程中已经存在swift文件可不添加，否则请直接在Xcode工程中任意位置创建任意一个swift文件

### 添加依赖库（必要）

**配置规则：**

> 在Xcode工程中，选择你的工程设置项-->选中"TARGETS"一栏对应的Target-->在"Build Phases"标签栏的-->"Link Binary With Libraries"添加

- 无

### Other Linker Flags（必要）

**配置规则：**

> 在Xcode工程中，选择你的工程设置项-->选中"TARGETS"一栏对应的Target-->在"Build Phases"标签栏的-->"Build Settings"搜索 Other Linker Flags 并添加
>
> ![linker](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504091756997.png)

- **-ObjC**

### 设置Info.plist

**配置规则：**

> 在Xcode工程中，选择你的工程设置项-->选中"TARGETS"一栏对应的Target-->在"info"标签栏的

#### URL Schemes

**配置规则：**

> 在Xcode工程中，选择你的工程设置项-->选中"TARGETS"一栏对应的Target-->在"info"标签栏的-->"URL Types"

##### URL Schemes = "app"+"appid" （必要）

> 例如：appid 为：352e8711bcca025e07230a8402f03d09
>
> 则配置对应的 URL schemes：app352e8711bcca025e07230a8402f03d09
>
> ![app352e8711bcca025e07230a8402f03d09](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081827815.png)

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>app352e8711bcca025e07230a8402f03d09</string>
    </array>
  </dict>
</array>
```

#### 权限（必要）

**配置规则：**

> 在Xcode工程中，选择你的工程设置项-->选中"TARGETS"一栏对应的Target-->在"info"标签栏的-->"URL Types"-->""Custom iOS Target Properties"
>
> 以下两个是SDK所需权限，根据产品发售地区请按需选择当地语言进行翻译，以下是其他（权限包括以下两种权限）的所有地区翻译：
>
> **闲闲SDK-海外版-IOS-Info.plist权限多语言**：[https://obqf4llj8m.feishu.cn/docx/Z7bBdu5jVoA64jxkXmrcw3UKnOb](https://obqf4llj8m.feishu.cn/docx/Z7bBdu5jVoA64jxkXmrcw3UKnOb)

##### 配置获取IDFA权限（必要）

> **NSUserTrackingUsageDescription**
>
> ![idfa](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081919728.png)

```xml
<key>NSUserTrackingUsageDescription</key>
<string>App需要访问您的设备标识符(IDFA)，用于提供更契合您兴趣的内容，减少无关广告推荐</string>
```

##### 配置获取Photo权限（必要）

>  **NSPhotoLibraryAddUsageDescription**
>
>  ![photo](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081924488.png)

```xml
<key>NSPhotoLibraryAddUsageDescription</key>
<string>APP需要您的同意，才能访问相册进行保存账号密码截图到您的相册，如禁止将无法完成保存操作</string>
```

### PrivacyInfo.xcprivacy

根据2024年5月1日Apple新隐私条款：

[https://developer.apple.com/documentation/bundleresources/privacy-manifest-files?language=objc](https://developer.apple.com/documentation/bundleresources/privacy-manifest-files?language=objc)

[https://developer.apple.com/support/third-party-SDK-requirements/](https://developer.apple.com/support/third-party-SDK-requirements/)

做哪些：

- 工程中添加 PrivacyInfo.xcprivacy
- 第三方库中添加 PrivacyInfo.xcprivacy

参考获取文件方式：

- [https://github.com/kimbely0320/update_privacy_info.py](https://github.com/kimbely0320/update_privacy_info.py)

- [https://www.privacymanifest.dev/](https://www.privacymanifest.dev/)

## SDK使用方式

### 启动（必要）

如使用AppDelegate：

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [XXGPlayOS xxpk_didFinishLaunchingWithOptions:launchOptions xconnectOptions:nil];
    
    return YES;
}
```

如使用SceneDelegate：

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

- (void)scene:(UIScene *)scene willConnectToSession:(UISceneSession *)session options:(UISceneConnectionOptions *)connectionOptions {
    // 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现
    [XXGPlayOS xxpk_didFinishLaunchingWithOptions:nil xconnectOptions:connectionOptions];
}
```

***<font color=red>注意：</font>*** **！！需要注意的是，以上两种方式，根据接入工程时机情况，选择对应方案即可，且需要注意，两种方案透传给SDK的数据结构不同，AppDelegate方案需要传launchOptions，SceneDelegate方案需要传connectionOptions。**

### App通用链接回调（必要）

如使用AppDelegate：

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [XXGPlayOS xxpk_applicationOpenURL:url xoptions:options xURLContexts:nil];
    return YES;
}
```

如使用SceneDelegate：

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

- (void)scene:(UIScene *)scene openURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts {
  
    // 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现
    [XXGPlayOS xxpk_applicationOpenURL:URLContexts.allObjects.firstObject.URL xoptions:nil xURLContexts:URLContexts];
}
```

### 设置SDK代理（必要）

> **设置代理用于接收登录、退出、支付、上报角色结果**

```objective-c
// 1.引入
#import <XXGPlayKitOS/XXGPlayKitOS.h>
// 2.遵循协议
@interface ViewController ()<XXGPlayDelegate>
// 3.设置代理
[XXGPlayOS xxpk_setPlayDelegate:self];

// 4.实现代理方法
// MARK: XXGPlayDelegate
///登录回调（必接）
- (void)xxpk_comeinFinish:(NSDictionary *)box {
    NSLog(@"登录成功 - %@", box);
}

//退出回调（必接）
// 必接 - 对接方需在此回调中调用游戏切换账号接口,退到游戏【登录界面】
// SDK个人中心有切换账号功能，所以用户在个人中心操作切换账号需游戏一并退出
// 另外游戏内退出同时调用SDK的退出登录接口 "xxpk_logout" 函数也会走此回调，
// 所以建议游戏的退出接口直接调用SDK的退出登录接口 "xxpk_logout" 在此回调中一并退出，不然游戏会重复退出2次
- (void)xxpk_logouted {
    NSLog(@"退出成功");
}

//支付回调
- (void)xxpk_payFinished:(BOOL)isSuc {
    NSLog(@"支付%@", isSuc?@"成功":@"失败");
}

//上报角色回调
- (void)xxpk_uploadRoleFinished:(BOOL)isSuc {
    NSLog(@"上报角色%@", isSuc?@"成功":@"失败");
}
```

### 登录（必要）

- 在游戏登录界面加载完成后调用及自动登录.(也可通过登录按钮让用户点击完成登录操作)
- 登录完成后会通过代理方法**"xxpk_comeinFinish: "**返回用户信息，类型为NSDictionary

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

/**
 * 在游戏登录界面加载完成后调用及自动登录.(也可通过登录按钮让用户点击完成登录操作)
 * 登录完成后会通过代理方法：xxpk_comeinFinish: 返回用户信息
 */
// 登录接口
[XXGPlayOS xxpk_comein];
```
- xxpk_comeinFinish:回调返回参数示例：

```json
{
    "id" : "62072919",
    "name" : "G25040962072919",
    "time" : "1744192861",
    "token" : "7fdf2ff6b4ba44f0284b2adada2652bb",
    "type" : 0
}
```

### 退出登录

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

// 退出接口
[XXGPlayOS xxpk_logout];
```

- xxpk_logouted 方法回调退出

### 支付（必要）
> 订单模型,具体查看属性注释和文档说明  
> 这里注意：确保传进的参数都为**NSString**类型，不接受NSNumber和其他类型
> 支付结果通过代理方法**"xxpk_payFinished:"**返回 YES为成功，NO为失败

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSString *cpOrderId = NSUUID.UUID.UUIDString;        // 游戏生成的订单号 (必传)
NSString *productCode = @"com.xxgame.sdk.demo.test"; // 商品标识(苹果商品标识)(必传)
NSString *amount = @"6";                             // 商品金额（单位：元） (必传)
NSString *productName = @"6元套餐";                   // 商品名称、例：60元宝 (必传)
NSString *serverId = @"20190927001";                 // 用户游戏角色所在的服务器id (必传)
NSString *roleId = @"100001";                        // 用户游戏角色ID (必传)
NSString *roleName = @"角色-XXGameSDK";               // 用户游戏角色名称 (必传)
NSString *roleLevel = @"99";                          // 用户游戏角色等级 (必传)
NSString *extraInfo = @"2019";                        // 订单额外信息，最终将回传给游戏服务器 (选传)

/**
 * 支付,传入订单模型
 * 异步回调支付结果
 */
[XXGPlayOS xxpk_createOrder:cpOrderId
         xxpk_productCode:productCode
              xxpk_amount:amount
         xxpk_productName:productName
            xxpk_serverId:serverId
           xxpk_extraInfo:extraInfo
              xxpk_roleId:roleId
            xxpk_roleName:roleName
           xxpk_roleLevel:roleLevel];
```

### 上报角色信息（必要）

如服务端已对接此接口客户端无需对接

> 调用时机(重要!!只在以下三个场景需调用):  
>  1、玩家在选择区服进入游戏时调用该接口
>  2、创建角色时调用该接口
>  3、角色升级或其他角色汇报信息发生变化时调用该接 


```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

/**
 * 必要！！！（有 3 处需要调用此接口）
 * 必要！！！（有 3 处需要调用此接口）
 * 必要！！！（有 3 处需要调用此接口） 
 * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型，extend字段类型为字典
 * 调用时机(重要!!只在以下三个场景需调用):
 * 1. 玩家在选择区服进入游戏时调用该接口。
 * 2. 创建角色时调用该接口
 * 3. 角色升级或其他角色汇报信息发生变化时调用该接
 */
// 角色所在服务器id (必传)
NSString *serverId = "9001";
// 区服名称 (必传)
NSString *serverName = @"XXGame";
// 用户游戏角色ID (必传)
NSString *roleId = "xx100921";
// 角色名称 (必传)
NSString *roleName = "阿斯蒂芬";
// 角色等级 (必传)
NSString *roleLevel = "109";
// 角色扩展信息（选填）类型：字典
NSDictionary *extend = @{
    @"pet": "5",          //宠物等级（5）
    @"horse": "1",        //坐骑等级（1）
    @"power": "10092",        //战力（100）
    @"promote": "2",      //转职（2转）
    @"married": "0",      //'0': 未婚, '1': 已婚
    @"liveness": "2000"),     //活跃度 (2000)
    @"hero_level": @"98",               //英雄等级(98级)
    @"trumps": @[ //已激活的法宝列表
         @"fabao1",   //法宝1
         @"fabao2"    //法宝2
    ],
    @"wings": @[  //已激活的翅膀列表
         @"wing1",    //翅膀1
         @"wing2"     //翅膀2
    ],
    @"artifacts": @[  //已激活的神器列表
         @"artifact1",    //神器1
         @"artifact2",    //神器2
    ],
    //@"xxx":@"xxx"                     //其他自定义信息
    //@"xxx":@"xxx"                     //其他自定义信息
    //@"xxx":@"xxx"                     //其他自定义信息
    //...
};

/**
 上报角色信息
 */
[XXGPlayOS xxpk_uploadRoleInfo:serverId
               xxpk_serverName:serverName
                   xxpk_roleId:roleId
                 xxpk_roleName:roleName
                xxpk_roleLevel:roleLevel
                   xxpk_extend:extend];
```

### 上报日志（选接）

> type:任意String类型
>
> content:任意String类型

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSString *type = @"login";
NSString *content = @"success";
[XXGPlayOS xxpk_reportlogWithType:type xxpk_content:content];
```

### 翻译（选接）

> _originalLanguage：需要翻译语言
>
> _targetLanguage：目标语言
>
> _toBeTranslated：待翻译文本
>
> 翻译支持语言查看：[https://obqf4llj8m.feishu.cn/docx/BxNcdPIkoopsVnx54zxc8A7vnEh ](https://obqf4llj8m.feishu.cn/docx/BxNcdPIkoopsVnx54zxc8A7vnEh)

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSString *_originalLanguage = @"zh";     // 需要翻译语言
NSString *_targetLanguage = @"en";       // 目标语言
NSString *_toBeTranslated = @"你好世界！"; // 待翻译文本
[XXGPlayOS xxpk_translateOriginalLanguage:_originalLanguage
                           targetLanguage:_targetLanguage
                           toBeTranslated:_toBeTranslated complate:^(NSString * _Nonnull translated, NSString * _Nullable error) {
    if (error) {
        // 翻译失败
        [self demo_log:[NSString stringWithFormat:@"翻译失败：%@", error]];
    }else {
        // 翻译失败
        [self demo_log:[NSString stringWithFormat:@"翻译成功：%@", translated]];
    }
}];
```

### 其他配置

#### 登录界面关闭接口（可选接口）

> 隐藏SDK界面右上角关闭按钮默认为：YES 隐藏状态
>
> 如果游戏登录界面没有登录按钮用来拉起SDK登录界面，不用调用或设置为YES，以免用户关闭后无法重新拉起登录界面

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

[XXGSetting xxpk_setCloseButtonHidden:YES];
```

=== [Facebook篇] ===

## Facebook SDK 集成

### 参数准备

- FacebookAppID
- FacebookDisplayName
- FacebookClientToken

### URL Schemes 配置

#### URL Schemes = "fb"+"FacebookAppID" 

> 例如：FacebookAppID 为：804492424288162
>
> 则配置对应的 URL schemes：fb804492424288162
>
> ![fbscheme](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111222958.png)

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>fb804492424288162</string>
    </array>
  </dict>
</array>
```

### 白名单配置

#### Facebook白名单

> Facebook登录、分享等功能使用
>
> ![白名单fb](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111617967.png)

```xml
<key>LSApplicationQueriesSchemes</key>
<array>
  <string>fb</string>
  <string>fbapi</string>
  <string>fb-messenger-share-api</string>
  <string>fbauth2</string>
  <string>fbshareextension</string>
</array>
```

### 其他配置

#### Facebook参数配置

>  用于FacebookSDK初始化使用
>
>  ![fb](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505151324950.png)

```xml
<key>FacebookAppID</key>
<string>我方运营提供</string>
<key>FacebookDisplayName</key>
<string>我方运营提供或跟应用名称一致</string>
<key>FacebookClientToken</key>
<string>我方运营提供</string>
<key>FacebookAutoLogAppEventsEnabled</key>
<true/>
<key>FacebookAdvertiserIDCollectionEnabled</key>
<true/>
```

### Facebook功能接口

#### 绑定Facebook（选接）

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

[XXGPlayOS xxpk_facebookBind:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
    if (userInfo) {
        // 绑定成功
        NSLog(@"绑定 Facebook 成功！\nuserInfo:%@",userInfo);
    }else {
        // 绑定失败
        NSLog(@"绑定 Facebook 失败! \nerrorMsg:%@", errorMsg);
    }
}];
```

#### Facebook事件打点

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSDictionary *params = @{@"param_key":@"param_value"};
// Facebook
[XXGPlayOS xxpk_logFacebookEvent:@"demo_eventName" params:params];
```

=== [VK篇] ===

## VK SDK 集成

### 参数准备

- VK_APPID // 配置 `URL Scheme `处使用

### URL Schemes 配置

#### URL Schemes = "vk"+"VK_APPID"

> 例如：VK_APPID 为：52832469
>
> 则配置对应的 URL schemes：vk52832469
>
> ![vkscheme](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111224219.png)

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>vk52832469</string>
    </array>
  </dict>
</array>
```

### 白名单配置

#### VK登录白名单

> VK白名单
>
> ![白名单vk](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111618111.png)

```xml
<key>LSApplicationQueriesSchemes</key>
<array>
  <string>vkauthorize-silent</string>
</array>
```

### VK功能接口

#### 绑定VK（选接）

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

[XXGPlayOS xxpk_VKBind:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
    if (userInfo) {
        // 绑定成功
        NSLog(@"绑定 VK 成功！userInfo:%@",userInfo);
    }else {
        // 绑定失败
        NSLog(@"绑定 VK 失败! errorMsg:%@", errorMsg);
    }
}];
```

=== [Firebase篇] ===

## Firebase SDK 集成

### Firebase功能接口

#### Firebase事件打点

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSDictionary *params = @{@"param_key":@"param_value"};
// Firebase
[XXGPlayOS xxpk_logFirebaseEvent:@"demo_eventName" params:params];
```

=== [AppsFlyer篇] ===

## AppsFlyer SDK 集成

### 其他配置

#### AppsFlyer配置

> 归因 App Clip
>
> ![af](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111657571.png)

```xml
<key>NSAdvertisingAttributionReportEndpoint</key>
<string>https://appsflyer-skadnetwork.com/</string>
```

### AppsFlyer功能接口

#### AppsFlyer事件打点

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSDictionary *params = @{@"param_key":@"param_value"};
// AppFlyer
[XXGPlayOS xxpk_logAppFlyerEvent:@"demo_eventName" params:params];
```

=== [AppLovin篇] ===

## AppLovin SDK 集成

### 参数准备

- GADApplicationIdentifier

### 其他配置

#### AppLovin参数配置

> **GADApplicationIdentifier**
>
> Value：ADMOB_APP_ID
>
> ![GADApplicationIdentifier](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081421096.png)

```xml
<key>GADApplicationIdentifier</key>
<string>我方运营提供</string>
```

**如果接入AppLovin还需以下配置：**

1. 接入 AppLovin 需要使用包管理工具 CocoaPods 接入，将下面脚本复制到Podfile。如果未提供了GADApplicationIdentifier 参数则 pod 'AppLovinMediationGoogleAdapter'  可以去除

> 这里pod install，需要给终端搭梯子，命令中端口号需要更换为电脑本地开启的代理端口例如：7890。
>
> 如何查看代理端口？打开电脑设置---网络（WIFI）---详情信息---代理---查看，然后更换成对应代理，执行下面命令
>
> export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
>
> 再终端执行下面命令  `pod install --repo-update`

```ruby
platform :ios, '13.0'

use_frameworks!
inhibit_all_warnings!

target 'YOUR_PROJECT_NAME' do
  pod 'AppLovinSDK'
  pod 'AppLovinMediationGoogleAdapter'
  pod 'AppLovinMediationIronSourceAdapter'
  pod 'AppLovinMediationFacebookAdapter'
  pod 'AppLovinMediationMintegralAdapter'
  pod 'AppLovinMediationByteDanceAdapter'
  pod 'AppLovinMediationUnityAdsAdapter'
  pod 'AppLovinMediationYandexAdapter'
end
```

2. 工程 **Build Setting** 搜索 **ENABLE_USER_SCRIPT_SANDBOXING** 设置为 **NO**

   ![ENABLE_USER_SCRIPT_SANDBOXING](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081717401.png)

3. 在 **Info.plist **添加 **SKAdNetworkItems** 

   > 下面链接是 **闲闲SDK-海外版-IOS-Info.plist-SKAdNetworkItems** 完整代码：
   >
   > 点击链接查看并复制到工程中：[https://obqf4llj8m.feishu.cn/docx/Aj4Zd9fxeoC4N2x8CIxcFumGnme?from=from_copylink](https://obqf4llj8m.feishu.cn/docx/Aj4Zd9fxeoC4N2x8CIxcFumGnme?from=from_copylink)

   ![SKAdNetworkItems](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081804623.png)

### AppLovin功能接口

#### 激励广告

> 为了防止激励广告音频干扰应用的背景音频，AppLovin 建议您在展示广告之前停止应用的背景音频。 关闭广告后，您可以恢复应用的背景音频。
>
> @param **customData** 自定义参数，可通过服务端接口获取

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

[XXGPlayOS xxpk_showRewardedAdForCustomData:@"customData" complate:^(BOOL result) {
    if (result) {
      // 完成激励广告任务回调
    }else {
      // 未完成激励广告
    }
}];
```

=== [Adjust篇] ===

## Adjust SDK 集成

### Adjust功能接口

#### Adjust事件打点

```objective-c
#import <XXGPlayKitOS/XXGPlayKitOS.h>

NSDictionary *params = @{@"param_key":@"param_value"};
// Adjust
[XXGPlayOS xxpk_logAdjustEvent:@"demo_eventName" params:params];
```

=== [Poopo篇] ===

## Poopo渠道 集成

### 权限配置

#### 配置获取相机权限

>  **NSCameraUsageDescription**
>
>  ![camera](https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505071706365.png)

```xml
<key>NSCameraUsageDescription</key>
<string>APP需要您的同意，才能访问您的相机权限，如禁止将不能在游戏内提供照相功能</string>
```

