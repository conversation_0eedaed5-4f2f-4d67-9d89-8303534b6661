"""
iMix - 通用iOS代码混淆工具

基于00001项目的核心逻辑，创建一个通用的iOS代码混淆工具，
去除定制化内容，提供标准化的混淆解决方案。

主要功能:
- 支持13种类型的代码元素混淆 (文件夹、文件、类名、属性、方法等)
- 完全复用00001项目的提取和过滤逻辑
- 提供CLI命令行接口，预留GUI图形界面
- 支持工程名修改和图片MD5混淆
- 详细的混淆日志和映射关系记录

版本: 1.0.0
作者: iMix Team
"""

__version__ = "1.0.0"
__author__ = "iMix Team"
__description__ = "通用iOS代码混淆工具"

# 导入主要模块
from .core import ObfuscationProcessor, FilterSystem, NameGenerator, ExtractorRegistry
from .config import ConfigManager
from .logging import ObfuscationLogger, Logger
from .cli import main as cli_main

__all__ = [
    "ObfuscationProcessor",
    "FilterSystem",
    "NameGenerator",
    "ExtractorRegistry",
    "ConfigManager",
    "ObfuscationLogger",
    "Logger",
    "cli_main",
    "__version__",
    "__author__",
    "__description__"
]
