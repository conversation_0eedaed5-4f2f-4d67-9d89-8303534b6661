#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的GUI调试版本
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class DebugGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GUI调试版本")
        self.root.geometry("800x600")
        
        print("初始化GUI...")
        
        # 初始化变量
        try:
            self.input_path = tk.StringVar(value="/Users/<USER>/jumbo/xianxian/XXGPlayKit")
            self.pbxproj_path = tk.StringVar()
            self.sdk_folder_path = tk.StringVar()
            self.group_name = tk.StringVar(value="SDK")
            print("✅ 变量初始化完成")
        except Exception as e:
            print(f"❌ 变量初始化失败: {e}")
            traceback.print_exc()
        
        # 创建界面
        try:
            self.create_ui()
            print("✅ 界面创建完成")
        except Exception as e:
            print(f"❌ 界面创建失败: {e}")
            traceback.print_exc()
    
    def create_ui(self):
        """创建用户界面"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="GUI调试版本", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 基本测试选项卡
        self.create_basic_tab(notebook)
        
        # 项目工具选项卡
        self.create_project_tab(notebook)
    
    def create_basic_tab(self, notebook):
        """创建基本测试选项卡"""
        basic_frame = ttk.Frame(notebook, padding="15")
        notebook.add(basic_frame, text="基本测试")
        
        # 工程路径测试
        path_group = ttk.LabelFrame(basic_frame, text="工程路径测试", padding="15")
        path_group.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(path_group, text="工程路径:").pack(anchor=tk.W)
        
        path_frame = ttk.Frame(path_group)
        path_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.path_entry = ttk.Entry(path_frame, textvariable=self.input_path)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(path_frame, text="浏览", 
                               command=self.safe_browse_project_path)
        browse_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 测试按钮组
        test_group = ttk.LabelFrame(basic_frame, text="测试按钮", padding="15")
        test_group.pack(fill=tk.X)
        
        ttk.Button(test_group, text="显示路径", 
                  command=self.show_path).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(test_group, text="测试对话框", 
                  command=self.test_dialog).pack(side=tk.LEFT)
    
    def create_project_tab(self, notebook):
        """创建项目工具选项卡"""
        project_frame = ttk.Frame(notebook, padding="15")
        notebook.add(project_frame, text="项目工具")
        
        # 项目文件配置
        pbx_group = ttk.LabelFrame(project_frame, text="项目文件", padding="15")
        pbx_group.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(pbx_group, text="project.pbxproj 文件路径:").pack(anchor=tk.W)
        
        pbx_frame = ttk.Frame(pbx_group)
        pbx_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.pbx_entry = ttk.Entry(pbx_frame, textvariable=self.pbxproj_path)
        self.pbx_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        pbx_browse_btn = ttk.Button(pbx_frame, text="浏览", 
                                   command=self.safe_browse_pbxproj_file)
        pbx_browse_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # SDK文件夹配置
        sdk_group = ttk.LabelFrame(project_frame, text="SDK文件夹", padding="15")
        sdk_group.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(sdk_group, text="SDK文件夹路径:").pack(anchor=tk.W)
        
        sdk_frame = ttk.Frame(sdk_group)
        sdk_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.sdk_entry = ttk.Entry(sdk_frame, textvariable=self.sdk_folder_path)
        self.sdk_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        sdk_browse_btn = ttk.Button(sdk_frame, text="浏览", 
                                   command=self.safe_browse_sdk_folder)
        sdk_browse_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 操作按钮
        action_group = ttk.LabelFrame(project_frame, text="操作", padding="15")
        action_group.pack(fill=tk.X)
        
        ttk.Button(action_group, text="显示所有路径", 
                  command=self.show_all_paths).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(action_group, text="测试添加SDK", 
                  command=self.test_add_sdk).pack(side=tk.LEFT)
    
    def safe_browse_project_path(self):
        """安全浏览工程路径"""
        print("开始浏览工程路径...")
        try:
            if not hasattr(self, 'input_path'):
                print("❌ input_path变量不存在")
                return
            
            path = filedialog.askdirectory(
                title="选择工程目录",
                initialdir=os.path.expanduser("~")
            )
            if path:
                self.input_path.set(path)
                print(f"✅ 选择了工程路径: {path}")
                messagebox.showinfo("成功", f"已设置工程路径:\n{path}")
            else:
                print("用户取消了选择")
        except Exception as e:
            print(f"❌ 浏览工程路径出错: {e}")
            traceback.print_exc()
            messagebox.showerror("错误", f"浏览工程路径时出错:\n{e}")
    
    def safe_browse_pbxproj_file(self):
        """安全浏览项目文件"""
        print("开始浏览项目文件...")
        try:
            if not hasattr(self, 'pbxproj_path'):
                print("❌ pbxproj_path变量不存在")
                return
            
            filetypes = [
                ("所有文件", "*.*"),
                ("pbxproj文件", "*.pbxproj")
            ]
            path = filedialog.askopenfilename(
                title="选择project.pbxproj文件",
                filetypes=filetypes,
                initialdir=os.path.expanduser("~")
            )
            if path:
                self.pbxproj_path.set(path)
                print(f"✅ 选择了项目文件: {path}")
                messagebox.showinfo("成功", f"已设置项目文件:\n{path}")
            else:
                print("用户取消了选择")
        except Exception as e:
            print(f"❌ 浏览项目文件出错: {e}")
            traceback.print_exc()
            messagebox.showerror("错误", f"浏览项目文件时出错:\n{e}")
    
    def safe_browse_sdk_folder(self):
        """安全浏览SDK文件夹"""
        print("开始浏览SDK文件夹...")
        try:
            if not hasattr(self, 'sdk_folder_path'):
                print("❌ sdk_folder_path变量不存在")
                return
            
            path = filedialog.askdirectory(
                title="选择SDK文件夹",
                initialdir=os.path.expanduser("~")
            )
            if path:
                self.sdk_folder_path.set(path)
                print(f"✅ 选择了SDK文件夹: {path}")
                messagebox.showinfo("成功", f"已设置SDK文件夹:\n{path}")
            else:
                print("用户取消了选择")
        except Exception as e:
            print(f"❌ 浏览SDK文件夹出错: {e}")
            traceback.print_exc()
            messagebox.showerror("错误", f"浏览SDK文件夹时出错:\n{e}")
    
    def show_path(self):
        """显示当前工程路径"""
        try:
            path = self.input_path.get()
            print(f"当前工程路径: {path}")
            messagebox.showinfo("路径信息", f"当前工程路径:\n{path}")
        except Exception as e:
            print(f"显示路径出错: {e}")
            traceback.print_exc()
    
    def show_all_paths(self):
        """显示所有路径"""
        try:
            project_path = self.input_path.get()
            pbx_path = self.pbxproj_path.get()
            sdk_path = self.sdk_folder_path.get()
            
            info = f"""当前配置:
工程路径: {project_path or '(未设置)'}
项目文件: {pbx_path or '(未设置)'}
SDK文件夹: {sdk_path or '(未设置)'}"""
            
            print(info)
            messagebox.showinfo("所有路径", info)
        except Exception as e:
            print(f"显示所有路径出错: {e}")
            traceback.print_exc()
    
    def test_dialog(self):
        """测试对话框"""
        try:
            result = messagebox.askyesno("测试", "这是一个测试对话框，是否继续？")
            print(f"对话框结果: {result}")
        except Exception as e:
            print(f"测试对话框出错: {e}")
            traceback.print_exc()
    
    def test_add_sdk(self):
        """测试添加SDK功能"""
        try:
            pbx_path = self.pbxproj_path.get()
            sdk_path = self.sdk_folder_path.get()
            
            if not pbx_path:
                messagebox.showerror("错误", "请先选择项目文件！")
                return
            
            if not sdk_path:
                messagebox.showerror("错误", "请先选择SDK文件夹！")
                return
            
            print("测试添加SDK功能...")
            print(f"项目文件: {pbx_path}")
            print(f"SDK文件夹: {sdk_path}")
            
            # 这里只是测试，不实际执行
            messagebox.showinfo("测试", f"测试添加SDK:\n项目文件: {pbx_path}\nSDK文件夹: {sdk_path}")
            
        except Exception as e:
            print(f"测试添加SDK出错: {e}")
            traceback.print_exc()
            messagebox.showerror("错误", f"测试出错:\n{e}")

def main():
    """主函数"""
    print("启动调试GUI...")
    try:
        root = tk.Tk()
        app = DebugGUI(root)
        print("GUI初始化完成，启动主循环...")
        root.mainloop()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 