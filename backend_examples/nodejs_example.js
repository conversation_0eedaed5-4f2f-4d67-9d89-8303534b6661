/**
 * Node.js后端调用OC混淆工具示例
 */

const { spawn, exec } = require('child_process');
const path = require('path');

class OCObfuscator {
    constructor(cliPath = './oc_cli') {
        this.cliPath = cliPath;
    }
    
    /**
     * 执行代码混淆
     */
    obfuscate(options = {}) {
        return new Promise((resolve, reject) => {
            let cmd = this.cliPath;
            
            // 添加参数
            if (options.input_path) {
                cmd += ` --input-path "${options.input_path}";
            }
            if (options.sdk_region) {
                cmd += ` --sdk-region "${options.sdk_region}";
            }
            if (options.new_project_name) {
                cmd += ` --new-project-name "${options.new_project_name}";
            }
            if (options.functions) {
                cmd += ` --functions "${options.functions}";
            }
            
            exec(cmd, (error, stdout, stderr) => {
                if (error) {
                    reject({
                        success: false,
                        error: error.message,
                        stderr: stderr,
                        command: cmd
                    });
                } else {
                    resolve({
                        success: true,
                        output: stdout,
                        stderr: stderr,
                        command: cmd
                    });
                }
            });
        });
    }
    
    /**
     * 使用spawn方式，支持实时输出
     */
    obfuscateWithProgress(options = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const args = [];
            
            if (options.input_path) {
                args.push('--input-path', options.input_path);
            }
            if (options.sdk_region) {
                args.push('--sdk-region', options.sdk_region);
            }
            if (options.new_project_name) {
                args.push('--new-project-name', options.new_project_name);
            }
            if (options.functions) {
                args.push('--functions', options.functions);
            }
            
            const child = spawn(this.cliPath, args);
            
            let output = '';
            let error = '';
            
            child.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                if (onProgress) {
                    onProgress(text);
                }
            });
            
            child.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            child.on('close', (code) => {
                if (code === 0) {
                    resolve({
                        success: true,
                        output: output,
                        error: error
                    });
                } else {
                    reject({
                        success: false,
                        output: output,
                        error: error,
                        code: code
                    });
                }
            });
        });
    }
}

// 使用示例
async function main() {
    try {
        const obfuscator = new OCObfuscator('./dist/oc_cli');
        
        console.log('开始混淆...');
        
        const result = await obfuscator.obfuscateWithProgress({
            input_path: '/path/to/ios/project',
            sdk_region: '2', 
            functions: '1,2,3'
        }, (progress) => {
            process.stdout.write(progress);
        });
        
        if (result.success) {
            console.log('\n混淆成功!');
        } else {
            console.error('混淆失败:', result.error);
        }
        
    } catch (error) {
        console.error('错误:', error);
    }
}

// 导出类
module.exports = OCObfuscator;

// 如果直接运行此文件
if (require.main === module) {
    main();
}