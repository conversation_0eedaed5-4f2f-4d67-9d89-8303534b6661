<?php
/**
 * PHP后端调用OC混淆工具示例
 */

class OCObfuscator {
    private $cli_path;
    
    public function __construct($cli_path = './oc_cli') {
        $this->cli_path = $cli_path;
    }
    
    /**
     * 执行代码混淆
     */
    public function obfuscate($options = []) {
        $cmd = escapeshellcmd($this->cli_path);
        
        // 添加参数
        if (isset($options['input_path'])) {
            $cmd .= ' --input-path ' . escapeshellarg($options['input_path']);
        }
        if (isset($options['sdk_region'])) {
            $cmd .= ' --sdk-region ' . escapeshellarg($options['sdk_region']);
        }
        if (isset($options['new_project_name'])) {
            $cmd .= ' --new-project-name ' . escapeshellarg($options['new_project_name']);
        }
        if (isset($options['functions'])) {
            $cmd .= ' --functions ' . escapeshellarg($options['functions']);
        }
        
        $output = [];
        $return_code = 0;
        exec($cmd . ' 2>&1', $output, $return_code);
        
        return [
            'success' => $return_code === 0,
            'output' => implode("\n", $output),
            'return_code' => $return_code,
            'command' => $cmd
        ];
    }
}

// 使用示例
try {
    $obfuscator = new OCObfuscator('./dist/oc_cli');
    
    $result = $obfuscator->obfuscate([
        'input_path' => '/path/to/ios/project',
        'sdk_region' => '2',
        'functions' => '1,2,3'
    ]);
    
    if ($result['success']) {
        echo "混淆成功!\n";
        echo $result['output'];
    } else {
        echo "混淆失败: " . $result['output'];
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>