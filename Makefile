# iMix iOS混淆工具 - Makefile
# 提供常用的开发和部署命令

.PHONY: help install install-dev test clean lint format build package run

# 默认目标
help:
	@echo "iMix iOS混淆工具 - 可用命令:"
	@echo ""
	@echo "开发命令:"
	@echo "  install      - 安装基础依赖"
	@echo "  install-dev  - 安装开发依赖"
	@echo "  test         - 运行测试"
	@echo "  lint         - 代码检查"
	@echo "  format       - 代码格式化"
	@echo ""
	@echo "构建命令:"
	@echo "  build        - 构建包"
	@echo "  package      - 打包可执行文件"
	@echo "  clean        - 清理构建文件"
	@echo ""
	@echo "运行命令:"
	@echo "  run          - 运行CLI (显示帮助)"
	@echo "  run-example  - 运行示例"
	@echo ""
	@echo "配置命令:"
	@echo "  config-show  - 显示当前配置"
	@echo "  config-init  - 生成配置文件"

# 安装基础依赖
install:
	@echo "安装基础依赖..."
	pip install -r requirements.txt

# 安装开发依赖
install-dev:
	@echo "安装开发依赖..."
	pip install -e .[dev]

# 运行测试
test:
	@echo "运行测试..."
	python -m pytest tests/ -v --cov=. --cov-report=html

# 代码检查
lint:
	@echo "运行代码检查..."
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
	mypy . --ignore-missing-imports

# 代码格式化
format:
	@echo "格式化代码..."
	black . --line-length=100
	@echo "代码格式化完成"

# 构建包
build:
	@echo "构建Python包..."
	python setup.py sdist bdist_wheel
	@echo "构建完成，文件位于 dist/ 目录"

# 打包可执行文件
package:
	@echo "打包可执行文件..."
	pip install pyinstaller
	pyinstaller --onefile --name imix cli/cli_main.py
	@echo "可执行文件生成完成，位于 dist/ 目录"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "清理完成"

# 运行CLI (显示帮助)
run:
	@echo "运行iMix CLI..."
	python cli/cli_main.py --help

# 运行示例 (验证命令)
run-example:
	@echo "运行示例 - 显示版本信息..."
	python cli/cli_main.py --version
	@echo ""
	@echo "运行示例 - 显示配置..."
	python cli/cli_main.py config show

# 显示当前配置
config-show:
	@echo "显示当前配置..."
	python cli/cli_main.py config show

# 生成配置文件
config-init:
	@echo "生成配置文件..."
	python cli/cli_main.py config init --output example_config.yaml
	@echo "配置文件已生成: example_config.yaml"

# 安装到系统
install-system:
	@echo "安装到系统..."
	pip install .
	@echo "安装完成，现在可以使用 'imix' 命令"

# 卸载
uninstall:
	@echo "卸载iMix..."
	pip uninstall imix -y

# 检查依赖
check-deps:
	@echo "检查依赖..."
	pip check
	@echo "依赖检查完成"

# 更新依赖
update-deps:
	@echo "更新依赖..."
	pip install --upgrade -r requirements.txt

# 生成依赖列表
freeze-deps:
	@echo "生成当前依赖列表..."
	pip freeze > requirements_frozen.txt
	@echo "依赖列表已保存到 requirements_frozen.txt"

# 开发环境设置
dev-setup: install-dev
	@echo "开发环境设置完成"
	@echo "可以开始开发了！"

# 发布前检查
pre-release: clean lint test build
	@echo "发布前检查完成"
	@echo "可以发布了！"
