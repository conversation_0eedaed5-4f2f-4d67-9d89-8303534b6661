{"description": "iMix随机名称生成词库", "version": "1.0.0", "word_dict": {"3": ["app", "api", "url", "web", "net", "tcp", "udp", "xml", "sql", "css", "dom", "gui", "cpu", "gpu", "ram", "rom", "usb", "led", "lcd", "ssd", "hdd", "pdf", "png", "jpg", "gif", "mp3", "mp4", "avi", "zip", "tar"], "4": ["data", "info", "user", "name", "text", "file", "path", "code", "type", "size", "time", "date", "list", "item", "node", "tree", "hash", "json", "http", "https", "mail", "chat", "game", "play", "stop", "save", "load", "edit", "copy", "move", "find", "sort", "sync", "async", "task", "work"], "5": ["model", "value", "index", "count", "total", "start", "begin", "final", "image", "video", "audio", "music", "sound", "color", "style", "theme", "event", "click", "touch", "press", "swipe", "slide", "scale", "rotate", "frame", "layer", "view", "scene", "stage", "level", "score", "point", "timer", "clock", "speed", "power", "energy", "force", "magic", "skill"], "6": ["object", "string", "number", "double", "float", "array", "vector", "matrix", "buffer", "memory", "thread", "process", "system", "window", "dialog", "button", "slider", "switch", "toggle", "picker", "scroll", "search", "filter", "result", "output", "render", "update", "refresh", "reload", "remove", "delete", "insert", "append", "modify", "change", "config", "option", "setting", "prefer", "custom", "default", "global"], "7": ["manager", "handler", "service", "factory", "builder", "creator", "adapter", "wrapper", "helper", "utility", "library", "package", "module", "plugin", "widget", "control", "element", "content", "message", "request", "response", "session", "context", "environment", "platform", "version", "release", "develop", "product", "project", "feature", "function", "method", "property", "variable", "constant", "parameter", "argument", "callback", "delegate", "protocol", "interface"], "8": ["instance", "template", "document", "resource", "database", "network", "internet", "protocol", "security", "password", "username", "account", "profile", "settings", "preferences", "configuration", "application", "framework", "component", "container", "collection", "dictionary", "algorithm", "structure", "pattern", "strategy", "observer", "listener", "provider", "consumer", "producer", "processor", "generator", "validator", "formatter", "converter", "transformer", "serializer", "deserializer"], "9": ["controller", "presenter", "viewmodel", "datamodel", "repository", "singleton", "prototype", "decorator", "composite", "mediator", "navigator", "coordinator", "scheduler", "dispatcher", "publisher", "subscriber", "notifier", "broadcaster", "multicaster", "unicaster", "allocator", "deallocator", "initializer", "finalizer", "destructor", "constructor", "accessor", "mutator", "iterator", "enumerator", "comparator", "evaluator", "calculator", "estimator", "predictor"], "10": ["background", "foreground", "middleware", "enterprise", "commercial", "opensource", "community", "developer", "programmer", "architect", "designer", "engineer", "scientist", "researcher", "analyst", "specialist", "consultant", "administrator", "supervisor", "coordinator", "facilitator", "moderator", "translator", "interpreter", "compiler", "assembler", "debugger", "profiler", "optimizer", "synthesizer"]}, "describe_words": {"property": ["attr", "prop", "field", "member", "var"], "method": ["func", "proc", "action", "operation", "task"], "class": ["obj", "entity", "model", "type", "kind"], "variable": ["val", "data", "info", "item", "element"], "constant": ["const", "static", "final", "fixed", "immutable"], "enum": ["type", "kind", "category", "group", "set"], "delegate": ["handler", "callback", "listener", "observer", "notifier"], "block": ["closure", "lambda", "function", "proc", "action"], "file": ["doc", "resource", "asset", "media", "content"], "folder": ["dir", "group", "category", "section", "module"], "image": ["pic", "photo", "icon", "logo", "graphic"], "string": ["text", "message", "content", "label", "title"]}}