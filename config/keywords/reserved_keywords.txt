# 保留关键词列表
# 这些关键词不会被混淆，包括Objective-C和Swift的关键词

# Objective-C 关键词
@interface
@implementation
@protocol
@property
@synthesize
@dynamic
@class
@selector
@encode
@synchronized
@try
@catch
@finally
@throw
@autoreleasepool
@available
@compatibility_alias
@defs
@optional
@required
@public
@private
@protected
@package

# C 关键词
auto
break
case
char
const
continue
default
do
double
else
enum
extern
float
for
goto
if
inline
int
long
register
restrict
return
short
signed
sizeof
static
struct
switch
typedef
union
unsigned
void
volatile
while
_Alignas
_Alignof
_Atomic
_Static_assert
_Noreturn
_Thread_local
_Generic

# Swift 关键词 (预留)
class
deinit
enum
extension
func
import
init
internal
let
operator
private
protocol
public
static
struct
subscript
typealias
var
break
case
continue
default
defer
do
else
fallthrough
for
guard
if
in
repeat
return
switch
where
while
as
catch
dynamicType
false
is
nil
rethrows
super
self
Self
throw
throws
true
try
associativity
convenience
dynamic
didSet
final
get
infix
indirect
lazy
left
mutating
none
nonmutating
optional
override
postfix
precedence
prefix
Protocol
required
right
set
Type
unowned
weak
willSet

# 系统方法前缀 (不混淆)
init
dealloc
alloc
new
copy
mutableCopy
description
debugDescription
hash
isEqual
performSelector
respondsToSelector
conformsToProtocol
isKindOfClass
isMemberOfClass

# 常用系统属性
delegate
dataSource
target
action
tag
frame
bounds
center
transform
alpha
hidden
userInteractionEnabled
multipleTouchEnabled
exclusiveTouch
clipsToBounds
backgroundColor
tintColor
contentMode
autoresizingMask
translatesAutoresizingMaskIntoConstraints
