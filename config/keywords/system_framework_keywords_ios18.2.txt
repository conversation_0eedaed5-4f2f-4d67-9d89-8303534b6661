# iOS 18.2 系统框架关键词
# 这个文件包含iOS系统框架的关键词，用于过滤，避免混淆系统API
# 注意: 这是一个占位符文件，实际使用时需要从00001项目复制完整的关键词列表

# Foundation框架常用类名
NSObject
NSString
NSMutableString
NSArray
NSMutableArray
NSDictionary
NSMutableDictionary
NSSet
NSMutableSet
NSNumber
NSDate
NSData
NSMutableData
NSURL
NSURLRequest
NSURLResponse
NSURLSession
NSURLSessionTask
NSURLSessionDataTask
NSURLSessionDownloadTask
NSURLSessionUploadTask
NSTimer
NSRunLoop
NSThread
NSOperation
NSOperationQueue
NSNotificationCenter
NSNotification
NSUserDefaults
NSBundle
NSFileManager
NSError
NSException
NSPredicate
NSExpression
NSFormatter
NSDateFormatter
NSNumberFormatter
NSRegularExpression
NSTextCheckingResult
NSValue
NSNull
NSProxy
NSInvocation
NSMethodSignature
NSCoder
NSKeyedArchiver
NSKeyedUnarchiver
NSPropertyListSerialization
NSJSONSerialization

# UIKit框架常用类名
UIApplication
UIApplicationDelegate
UIWindow
UIView
UIViewController
UINavigationController
UINavigationBar
UINavigationItem
UITabBarController
UITabBar
UITabBarItem
UITableView
UITableViewController
UITableViewCell
UICollectionView
UICollectionViewController
UICollectionViewCell
UICollectionViewLayout
UICollectionViewFlowLayout
UIScrollView
UILabel
UIButton
UIImageView
UITextField
UITextView
UISwitch
UISlider
UIProgressView
UIActivityIndicatorView
UIPickerView
UIDatePicker
UISegmentedControl
UIPageControl
UIToolbar
UIBarButtonItem
UIActionSheet
UIAlertView
UIAlertController
UIPopoverController
UIGestureRecognizer
UITapGestureRecognizer
UILongPressGestureRecognizer
UIPanGestureRecognizer
UIPinchGestureRecognizer
UIRotationGestureRecognizer
UISwipeGestureRecognizer
UIColor
UIFont
UIImage
UIBezierPath
UIScreen
UIDevice
UIResponder
UIEvent
UITouch
UIMenuController
UIMenuItem
UIPasteboard
UILocalNotification
UIUserNotificationSettings

# Core Graphics框架
CGRect
CGPoint
CGSize
CGFloat
CGAffineTransform
CGContext
CGPath
CGMutablePath
CGImage
CGImageRef
CGColorSpace
CGColorSpaceRef
CGColor
CGColorRef
CGGradient
CGGradientRef
CGPattern
CGPatternRef
CGFont
CGFontRef
CGPDFDocument
CGPDFPage

# Core Animation框架
CALayer
CAAnimation
CABasicAnimation
CAKeyframeAnimation
CAAnimationGroup
CATransition
CADisplayLink
CAShapeLayer
CATextLayer
CAGradientLayer
CAReplicatorLayer
CAScrollLayer
CATransformLayer
CAEmitterLayer
CAEmitterCell

# 注意: 这只是一个示例文件
# 实际项目中需要从00001项目的system_framework_keywords_ios18.2.txt文件复制完整内容
# 该文件应包含所有iOS 18.2系统框架的类名、方法名、属性名等关键词
