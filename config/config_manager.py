"""
配置管理器

负责加载、管理和验证配置文件，提供统一的配置访问接口。
基于ARCHITECTURE.md中的配置系统设计实现。
"""

import os
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path


class ConfigManager:
    """
    配置管理器类
    
    功能:
    - 加载默认配置和用户自定义配置
    - 提供配置项的访问接口
    - 验证配置的有效性
    - 支持配置的动态更新
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 自定义配置文件路径，如果为None则使用默认配置
        """
        self.config_data: Dict[str, Any] = {}
        self.config_path = config_path
        self.default_config_path = self._get_default_config_path()
        
        # 加载配置
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "default_config.yaml")
    
    def _load_config(self):
        """
        加载配置文件
        
        优先级: 用户配置 > 默认配置
        """
        # 首先加载默认配置
        self._load_default_config()
        
        # 如果指定了用户配置，则加载并合并
        if self.config_path and os.path.exists(self.config_path):
            self._load_user_config()
    
    def _load_default_config(self):
        """加载默认配置文件"""
        try:
            with open(self.default_config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
        except Exception as e:
            raise ConfigError(f"加载默认配置文件失败: {e}")
    
    def _load_user_config(self):
        """加载用户自定义配置文件并合并到默认配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f) or {}
                self._merge_config(user_config)
        except Exception as e:
            raise ConfigError(f"加载用户配置文件失败: {e}")
    
    def _merge_config(self, user_config: Dict[str, Any]):
        """
        合并用户配置到默认配置
        
        Args:
            user_config: 用户配置字典
        """
        def merge_dict(default: Dict, user: Dict) -> Dict:
            """递归合并字典"""
            result = default.copy()
            for key, value in user.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self.config_data = merge_dict(self.config_data, user_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项的值
        
        Args:
            key: 配置项键名，支持点号分隔的嵌套键名 (如: "project.name")
            default: 默认值
            
        Returns:
            配置项的值
        """
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置项的值
        
        Args:
            key: 配置项键名，支持点号分隔的嵌套键名
            value: 要设置的值
        """
        keys = key.split('.')
        config = self.config_data
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def get_project_filters(self) -> Dict[str, List[str]]:
        """获取项目过滤配置"""
        return self.get("project_filters", {})
    
    def get_obfuscation_rules(self) -> Dict[str, Any]:
        """获取混淆规则配置"""
        return self.get("obfuscation_rules", {})
    
    def get_extractor_config(self) -> Dict[str, Any]:
        """获取提取器配置"""
        return self.get("extractors", {})
    
    def get_keywords_config(self) -> Dict[str, Any]:
        """获取关键词配置"""
        return self.get("keywords", {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置"""
        return self.get("output", {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.get("performance", {})
    
    def is_extractor_enabled(self, extractor_name: str) -> bool:
        """
        检查指定提取器是否启用
        
        Args:
            extractor_name: 提取器名称
            
        Returns:
            是否启用
        """
        enabled_extractors = self.get("extractors.enabled", [])
        return extractor_name in enabled_extractors
    
    def get_extractor_priority(self, extractor_name: str) -> int:
        """
        获取提取器的优先级
        
        Args:
            extractor_name: 提取器名称
            
        Returns:
            优先级数值，数值越大优先级越高
        """
        priorities = self.get("extractors.priorities", {})
        return priorities.get(f"{extractor_name}_extractor", 0)
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 检查必要的配置项
            required_keys = [
                "project.name",
                "project.version", 
                "extractors.enabled",
                "keywords.system_framework_file",
                "output.log_level"
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    raise ConfigError(f"缺少必要的配置项: {key}")
            
            # 检查提取器配置
            enabled_extractors = self.get("extractors.enabled", [])
            if not enabled_extractors:
                raise ConfigError("至少需要启用一个提取器")
            
            # 检查日志级别
            log_level = self.get("output.log_level", "INFO")
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR"]:
                raise ConfigError(f"无效的日志级别: {log_level}")
            
            return True
            
        except ConfigError:
            raise
        except Exception as e:
            raise ConfigError(f"配置验证失败: {e}")
    
    def save_config(self, output_path: str):
        """
        保存当前配置到文件
        
        Args:
            output_path: 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            raise ConfigError(f"保存配置文件失败: {e}")
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def __repr__(self) -> str:
        """返回配置的详细字符串表示"""
        return f"ConfigManager(config_path={self.config_path}, keys={list(self.config_data.keys())})"


class ConfigError(Exception):
    """配置相关的异常类"""
    
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)
    
    def __str__(self) -> str:
        return f"配置错误: {self.message}"
