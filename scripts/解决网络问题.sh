#!/bin/bash

# 🌐 解决pip网络连接问题脚本
# 解决代理超时和网络连接问题

echo "🌐 pip网络问题解决方案"
echo "======================"

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="$PROJECT_ROOT/venv"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "🔍 问题诊断："
echo "   错误: ProxyError('Cannot connect to proxy.', TimeoutError)"
echo "   原因: pip尝试使用代理但连接超时"
echo ""

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 方案1: 临时禁用代理
echo "📡 方案1: 临时禁用代理安装"
echo "=========================="

# 激活虚拟环境
if [ -d "$VENV_PATH" ]; then
    source "$VENV_PATH/bin/activate"
else
    echo "🔧 创建虚拟环境..."
    python3 -m venv "$VENV_PATH"
    source "$VENV_PATH/bin/activate"
fi

echo "🚫 临时禁用代理环境变量..."
unset http_proxy
unset https_proxy
unset HTTP_PROXY
unset HTTPS_PROXY

echo "📦 尝试安装依赖包（无代理）..."
pip install --upgrade pip --no-proxy
pip install -r requirements.txt --no-proxy

if [ $? -eq 0 ]; then
    echo "✅ 方案1成功！依赖包安装完成"
    deactivate
    exit 0
fi

echo "❌ 方案1失败，尝试方案2..."
echo ""

# 方案2: 使用国内镜像源
echo "📡 方案2: 使用国内镜像源"
echo "======================"

echo "🇨🇳 使用清华大学镜像源..."
pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

if [ $? -eq 0 ]; then
    echo "✅ 方案2成功！使用清华镜像安装完成"
    deactivate
    exit 0
fi

echo "❌ 方案2失败，尝试方案3..."
echo ""

# 方案3: 使用阿里云镜像源
echo "📡 方案3: 使用阿里云镜像源"
echo "======================"

echo "☁️ 使用阿里云镜像源..."
pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

if [ $? -eq 0 ]; then
    echo "✅ 方案3成功！使用阿里云镜像安装完成"
    deactivate
    exit 0
fi

echo "❌ 方案3失败，尝试方案4..."
echo ""

# 方案4: 配置pip使用可信主机
echo "📡 方案4: 配置可信主机"
echo "===================="

echo "🔐 设置可信主机..."
pip install --upgrade pip --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org
pip install -r requirements.txt --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org

if [ $? -eq 0 ]; then
    echo "✅ 方案4成功！使用可信主机安装完成"
    deactivate
    exit 0
fi

echo "❌ 所有自动方案失败"
echo ""

# 提供手动解决方案
echo "🛠  手动解决方案"
echo "==============="
echo ""
echo "请尝试以下命令："
echo ""
echo "1. 创建pip配置文件："
echo "   mkdir -p ~/.pip"
echo "   cat > ~/.pip/pip.conf << 'EOF'"
echo "[global]"
echo "index-url = https://pypi.tuna.tsinghua.edu.cn/simple/"
echo "trusted-host = pypi.tuna.tsinghua.edu.cn"
echo "timeout = 120"
echo "EOF"
echo ""
echo "2. 然后运行："
echo "   source venv/bin/activate"
echo "   pip install -r requirements.txt"
echo ""
echo "3. 或者直接使用镜像源："
echo "   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn"

deactivate 