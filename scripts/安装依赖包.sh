#!/bin/bash

# 📦 混淆工具依赖包安装脚本
# 可以独立运行，或被启动脚本调用

echo "📦 Objective-C混淆工具 - 依赖包安装"
echo "====================================="

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="$PROJECT_ROOT/venv"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 虚拟环境路径: $VENV_PATH"

# 检查虚拟环境
if [ ! -d "$VENV_PATH" ]; then
    echo ""
    echo "🔧 创建虚拟环境..."
    python3 -m venv "$VENV_PATH"
fi

# 激活虚拟环境
echo "🎯 激活虚拟环境..."
source "$VENV_PATH/bin/activate"

# 升级pip
echo "📦 更新包管理器..."
pip install --upgrade pip --quiet

# 安装依赖包
if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
    echo ""
    echo "📚 安装项目依赖包..."
    echo "包含以下包："
    echo "  • pyyaml          - YAML配置文件处理"
    echo "  • pbxproj         - Xcode项目文件处理"
    echo "  • numpy           - 数据处理和plist操作"
    echo "  • markdown        - Markdown转HTML"
    echo "  • beautifulsoup4  - HTML解析"
    echo "  • pygments        - 代码高亮"
    echo "  • Pillow          - 图像处理"
    echo ""
    
    # 安装依赖
    pip install -r "$PROJECT_ROOT/requirements.txt"
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 所有依赖包安装成功！"
        
        # 验证安装
        echo ""
        echo "🧪 验证安装..."
        python -c "
import sys
packages = ['yaml', 'pbxproj', 'numpy', 'markdown', 'bs4', 'pygments', 'PIL']
success = []
failed = []

for pkg in packages:
    try:
        __import__(pkg)
        success.append(pkg)
    except ImportError:
        failed.append(pkg)

print(f'✅ 成功安装: {len(success)} 个包')
for pkg in success:
    print(f'   • {pkg}')

if failed:
    print(f'❌ 安装失败: {len(failed)} 个包')
    for pkg in failed:
        print(f'   • {pkg}')
else:
    print('🎉 所有包验证通过！')
"
        
    else
        echo ""
        echo "❌ 依赖包安装过程中出现错误"
        echo "请检查网络连接或包名称"
    fi
    
else
    echo "❌ 未找到 requirements.txt 文件"
    echo "查找路径: $PROJECT_ROOT/requirements.txt"
    exit 1
fi

# 退出虚拟环境
deactivate

echo ""
echo "🏁 安装完成！现在可以使用 ./scripts/启动GUI.sh 启动混淆工具" 