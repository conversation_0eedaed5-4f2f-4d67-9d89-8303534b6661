#!/bin/bash
# iMix 依赖安装脚本

set -e

echo "=== iMix 依赖安装脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ 错误: pip3 未安装"
    exit 1
fi

echo "✅ pip3 检查通过"

# 升级pip
echo "📦 升级pip..."
pip3 install --upgrade pip

# 安装依赖
echo "📦 安装项目依赖..."
pip3 install -r requirements.txt

# 检查关键依赖
echo "🔍 检查关键依赖..."
python3 -c "import yaml; print('✅ PyYAML 安装成功')"
python3 -c "import click; print('✅ Click 安装成功')"
python3 -c "import rich; print('✅ Rich 安装成功')"

# 安装开发依赖（可选）
read -p "是否安装开发依赖？(y/N): " install_dev
if [[ $install_dev =~ ^[Yy]$ ]]; then
    echo "📦 安装开发依赖..."
    pip3 install pytest pytest-cov black flake8 mypy
    echo "✅ 开发依赖安装完成"
fi

# 验证安装
echo "🧪 验证安装..."
if python3 -c "import imix; print('✅ iMix 模块导入成功')" 2>/dev/null; then
    echo "✅ iMix 安装验证成功"
else
    echo "⚠️  iMix 模块导入失败，可能需要设置PYTHONPATH"
    echo "   请运行: export PYTHONPATH=\$PYTHONPATH:\$(pwd)"
fi

echo ""
echo "🎉 依赖安装完成！"
echo ""
echo "快速开始:"
echo "  1. 分析项目: python -m imix.cli analyze /path/to/ios/project"
echo "  2. 生成配置: python -m imix.cli init-config"
echo "  3. 执行混淆: python -m imix.cli obfuscate /path/to/ios/project"
echo ""
echo "更多帮助: python -m imix.cli --help"
