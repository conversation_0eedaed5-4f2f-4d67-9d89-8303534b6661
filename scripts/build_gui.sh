#!/bin/bash
# build_gui.sh - GUI应用打包脚本
# 用于创建独立的桌面应用程序

echo "🎨 开始打包GUI应用..."

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查是否在正确的目录
if [ ! -f "oc_gui.py" ]; then
    echo "❌ 错误：未找到 oc_gui.py 文件"
    echo "当前目录: $(pwd)"
    echo "请确保脚本能正确定位项目根目录"
    exit 1
fi

# 激活虚拟环境
if [ -d "venv" ]; then
    echo "📦 激活虚拟环境..."
    source venv/bin/activate
else
    echo "❌ 错误：未找到虚拟环境"
    echo "请先运行 ./scripts/安装依赖包.sh 创建虚拟环境"
    exit 1
fi

# 安装PyInstaller
echo "📦 安装 PyInstaller..."
pip install pyinstaller

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
rm -rf dist/*混淆工具* build/*混淆工具* oc_gui.spec

# 检测操作系统
OS=$(uname -s)
echo "🖥️  检测到操作系统: $OS"

# 根据操作系统设置参数
if [[ "$OS" == "Darwin" ]]; then
    # macOS
    ICON_FLAG=""
    if [ -f "icon.icns" ]; then
        ICON_FLAG="--icon=icon.icns"
    fi
    APP_NAME="OC代码混淆工具"
    echo "🍎 为 macOS 打包..."
elif [[ "$OS" == "Linux" ]]; then
    # Linux
    ICON_FLAG=""
    if [ -f "icon.png" ]; then
        ICON_FLAG="--icon=icon.png"
    fi
    APP_NAME="OC代码混淆工具"
    echo "🐧 为 Linux 打包..."
else
    # Windows (如果在Windows Subsystem for Linux中)
    ICON_FLAG=""
    if [ -f "icon.ico" ]; then
        ICON_FLAG="--icon=icon.ico"
    fi
    APP_NAME="OC代码混淆工具"
    echo "🪟 为 Windows 打包..."
fi

# 打包GUI应用
echo "🔨 开始打包..."
if [[ -n "$ICON_FLAG" ]]; then
    pyinstaller \
        --onefile \
        --windowed \
        --name="$APP_NAME" \
        --add-data "ObjectiveC:ObjectiveC" \
        --add-data "配置文件:配置文件" \
        --hidden-import yaml \
        --hidden-import pbxproj \
        --hidden-import numpy \
        --hidden-import tkinter \
        --hidden-import markdown \
        --hidden-import bs4 \
        --hidden-import pygments \
        --clean \
        $ICON_FLAG \
        oc_gui.py
else
    pyinstaller \
        --onefile \
        --windowed \
        --name="$APP_NAME" \
        --add-data "ObjectiveC:ObjectiveC" \
        --add-data "配置文件:配置文件" \
        --hidden-import yaml \
        --hidden-import pbxproj \
        --hidden-import numpy \
        --hidden-import tkinter \
        --hidden-import markdown \
        --hidden-import bs4 \
        --hidden-import pygments \
        --clean \
        oc_gui.py
fi

# 检查打包结果
if [[ "$OS" == "Darwin" ]] && [ -d "dist/$APP_NAME.app" ]; then
    echo "✅ macOS应用包打包完成！"
    echo "📁 输出位置: dist/$APP_NAME.app"
    echo "📊 应用大小: $(du -sh "dist/$APP_NAME.app" | cut -f1)"
    
    echo "🎉 打包成功！"
    echo "📖 使用方法:"
    echo "   双击运行: dist/$APP_NAME.app"
    echo "   命令行运行: open \"dist/$APP_NAME.app\""
    
elif [ -f "dist/$APP_NAME" ]; then
    echo "✅ GUI应用打包完成！"
    echo "📁 输出位置: dist/$APP_NAME"
    echo "📊 文件大小: $(du -h "dist/$APP_NAME" | cut -f1)"
    
    # 设置执行权限
    chmod +x "dist/$APP_NAME"
    
    echo "🎉 打包成功！"
    echo "📖 使用方法:"
    echo "   双击运行: dist/$APP_NAME"
    echo "   命令行运行: ./dist/$APP_NAME"
    
else
    echo "❌ 打包失败！"
    echo "请检查错误信息并重试"
    exit 1
fi

echo ""
echo "💡 提示："
echo "   - 可执行文件自带Python运行环境，无需安装Python"
echo "   - 可以在没有安装项目依赖的机器上直接运行"
echo "   - 适合分发给其他用户使用" 