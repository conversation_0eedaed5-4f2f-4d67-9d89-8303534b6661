#!/bin/bash

# 🎯 Objective-C混淆工具运行脚本
# 自动激活虚拟环境并运行start.py

echo "🎯 Objective-C混淆工具 (CLI模式)"
echo "================================"

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="$PROJECT_ROOT/venv"

echo "📁 项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查虚拟环境
if [ ! -d "$VENV_PATH" ]; then
    echo "❌ 未找到虚拟环境"
    echo "   请先运行: ./scripts/启动GUI.sh 或 ./scripts/安装依赖包.sh"
    exit 1
fi

# 检查start.py
if [ ! -f "ObjectiveC/oc_start.py" ]; then
    echo "❌ 未找到 oc_start.py 文件"
    echo "   查找路径: $PROJECT_ROOT/ObjectiveC/oc_start.py"
    exit 1
fi

echo "🎯 激活虚拟环境..."
source "$VENV_PATH/bin/activate"

echo "📦 检查依赖包..."
python -c "import yaml, pbxproj, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要的依赖包"
    echo "   请先运行: ./scripts/安装依赖包.sh"
    deactivate
    exit 1
fi

echo "✅ 依赖包检查通过"
echo ""
echo "🚀 启动混淆工具..."
echo "   (CLI命令行模式)"
echo ""

# 设置Python路径，确保可以导入ObjectiveC模块
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 运行start.py
python ObjectiveC/oc_start.py "$@"
exit_code=$?

# 退出虚拟环境
deactivate

if [ $exit_code -eq 0 ]; then
    echo ""
    echo "✅ 混淆工具运行完成"
else
    echo ""
    echo "⚠️  混淆工具运行出现异常 (退出代码: $exit_code)"
fi

echo "🏁 完成" 