#!/bin/bash
# 快速打包.sh - 一键打包所有内容
# 使用最简单的方式打包CLI和GUI应用

echo "🚀 OC混淆工具快速打包"
echo "=================================="

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version

# 激活虚拟环境
if [ -d "venv" ]; then
    echo "📦 激活虚拟环境..."
    source venv/bin/activate
else
    echo "❌ 未找到虚拟环境，请先运行 ./scripts/安装依赖包.sh"
    exit 1
fi

# 选择打包选项
echo ""
echo "请选择打包选项："
echo "1) 只打包CLI工具（用于后端集成）"
echo "2) 只打包GUI应用（桌面应用）"
echo "3) 打包所有内容（推荐）"
echo "4) 只创建后端示例"
echo ""
read -p "请输入选项 (1-4，默认3): " choice

case $choice in
    1)
        echo "🔧 打包CLI工具..."
        python3 scripts/build_universal.py --cli
        ;;
    2)
        echo "🎨 打包GUI应用..."
        python3 scripts/build_universal.py --gui
        ;;
    3|"")
        echo "📦 打包所有内容..."
        python3 scripts/build_universal.py --all
        ;;
    4)
        echo "📝 创建后端示例..."
        python3 scripts/build_universal.py --examples
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "=================================="
echo "🎉 打包完成！"
echo ""
echo "📁 输出文件:"
if [ -f "dist/oc_cli" ]; then
    echo "   - CLI工具: dist/oc_cli"
    echo "     使用方法: ./dist/oc_cli --help"
    echo "     后端调用: exec('./dist/oc_cli --input-path /path/to/project')"
fi

if [ -f "dist/OC代码混淆工具" ]; then
    echo "   - GUI应用: dist/OC代码混淆工具"
    echo "     使用方法: ./dist/OC代码混淆工具"
elif [ -d "dist/OC代码混淆工具.app" ]; then
    echo "   - macOS应用: dist/OC代码混淆工具.app"
    echo "     使用方法: open dist/OC代码混淆工具.app"
fi

if [ -d "backend_examples" ]; then
    echo "   - 后端示例: backend_examples/"
    echo "     PHP示例: backend_examples/php_example.php"
    echo "     Node.js示例: backend_examples/nodejs_example.js"
fi

echo ""
echo "💡 提示:"
echo "   - 可执行文件自带Python运行环境，无需依赖系统"
echo "   - 可以在其他机器上直接运行，无需安装Python或依赖"
echo "   - 适合分发给用户或集成到后端系统" 