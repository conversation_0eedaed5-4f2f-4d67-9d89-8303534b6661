#!/bin/bash
# build_cli.sh - CLI工具打包脚本
# 用于创建独立的命令行工具，供后端系统调用

echo "🔧 开始打包CLI工具..."

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查是否在正确的目录
if [ ! -f "oc_cli.py" ]; then
    echo "❌ 错误：未找到 oc_cli.py 文件"
    echo "当前目录: $(pwd)"
    echo "请确保脚本能正确定位项目根目录"
    exit 1
fi

# 激活虚拟环境
if [ -d "venv" ]; then
    echo "📦 激活虚拟环境..."
    source venv/bin/activate
else
    echo "❌ 错误：未找到虚拟环境"
    echo "请先运行 ./scripts/安装依赖包.sh 创建虚拟环境"
    exit 1
fi

# 安装PyInstaller
echo "📦 安装 PyInstaller..."
pip install pyinstaller

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
rm -rf dist/oc_cli* build/oc_cli* oc_cli.spec

# 打包CLI工具
echo "🔨 开始打包..."
pyinstaller \
    --onefile \
    --name="oc_cli" \
    --console \
    --add-data "ObjectiveC:ObjectiveC" \
    --add-data "配置文件:配置文件" \
    --exclude-module tkinter \
    --exclude-module matplotlib \
    --exclude-module pandas \
    --hidden-import yaml \
    --hidden-import pbxproj \
    --hidden-import numpy \
    --hidden-import markdown \
    --hidden-import bs4 \
    --hidden-import pygments \
    --hidden-import PIL \
    --hidden-import PIL.Image \
    --clean \
    oc_cli.py

# 检查打包结果
if [ -f "dist/oc_cli" ]; then
    echo "✅ CLI工具打包完成！"
    echo "📁 输出位置: dist/oc_cli"
    echo "📊 文件大小: $(du -h dist/oc_cli | cut -f1)"
    
    # 设置执行权限
    chmod +x dist/oc_cli
    
    # 测试可执行文件
    echo "🧪 测试可执行文件..."
    if ./dist/oc_cli --help > /dev/null 2>&1; then
        echo "✅ CLI工具测试通过！"
        echo ""
        echo "🎉 打包成功！可执行文件位于: dist/oc_cli"
        echo "📖 使用方法:"
        echo "   ./dist/oc_cli --help"
        echo "   ./dist/oc_cli --input-path /path/to/project --sdk-region 2"
        echo ""
        echo "🚀 后端集成示例:"
        echo "   PHP: exec('./dist/oc_cli --input-path \$path --sdk-region 2');"
        echo "   Node.js: exec('./dist/oc_cli --input-path ' + path + ' --sdk-region 2');"
    else
        echo "⚠️  警告：CLI工具测试失败，但文件已生成"
    fi
else
    echo "❌ 打包失败！"
    exit 1
fi 