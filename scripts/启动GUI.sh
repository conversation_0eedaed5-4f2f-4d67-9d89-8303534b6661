#!/bin/bash

# 🎨 Objective-C混淆工具 GUI启动器
# 自动管理虚拟环境，一键启动

echo "🚀 Objective-C混淆工具启动器"
echo "================================="

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="$PROJECT_ROOT/venv"

echo "📁 项目根目录: $PROJECT_ROOT"

# 检查虚拟环境
if [ ! -d "$VENV_PATH" ]; then
    echo ""
    echo "🔧 首次运行，正在创建项目专用环境..."
    echo "   这只需要几秒钟，以后启动会更快"
    
    # 创建虚拟环境
    python3 -m venv "$VENV_PATH"
    
    # 激活环境
    source "$VENV_PATH/bin/activate"
    
    # 升级pip
    echo "📦 更新包管理器..."
    pip install --upgrade pip --quiet
    
    # 安装项目依赖
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        echo "📚 安装项目依赖包..."
        echo "   (包括: pyyaml, pbxproj, numpy, markdown, beautifulsoup4, pygments, Pillow)"
        
        # 尝试正常安装
        pip install -r "$PROJECT_ROOT/requirements.txt" --quiet
        
        if [ $? -eq 0 ]; then
            echo "✅ 所有依赖包安装完成！"
        else
            echo "⚠️  网络问题，尝试使用国内镜像源..."
            # 尝试使用清华镜像源
            pip install -r "$PROJECT_ROOT/requirements.txt" -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet
            
            if [ $? -eq 0 ]; then
                echo "✅ 使用镜像源安装完成！"
            else
                echo "⚠️  依赖包安装失败，但GUI仍可运行基本功能"
                echo "   如需完整功能，请运行: ./scripts/解决网络问题.sh"
            fi
        fi
    else
        echo "⚠️  未找到 requirements.txt，跳过依赖安装"
    fi
    
    echo "✅ 环境创建完成！"
    echo ""
else
    echo "🎯 激活项目环境..."
    source "$VENV_PATH/bin/activate"
    
    # 检查是否需要更新依赖
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        echo "🔍 检查依赖包状态..."
        
        # 检查关键包是否已安装
        python -c "import yaml, pbxproj, numpy" 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "📚 发现缺失的依赖包，正在安装..."
            pip install -r "$PROJECT_ROOT/requirements.txt" --quiet
            
            if [ $? -eq 0 ]; then
                echo "✅ 依赖包更新完成！"
            else
                echo "⚠️  网络问题，尝试使用镜像源..."
                pip install -r "$PROJECT_ROOT/requirements.txt" -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet
                
                if [ $? -eq 0 ]; then
                    echo "✅ 使用镜像源更新完成！"
                else
                    echo "⚠️  依赖包安装失败，运行: ./scripts/解决网络问题.sh"
                fi
            fi
        else
            echo "✅ 所有依赖包已就绪"
        fi
    fi
fi

# 检查GUI文件
if [ ! -f "$PROJECT_ROOT/oc_gui.py" ]; then
    echo "❌ 错误：找不到 oc_gui.py"
    echo "   当前查找路径: $PROJECT_ROOT/oc_gui.py"
    echo "   请确保脚本能正确定位项目根目录"
    exit 1
fi

# 启动GUI
echo "🎨 启动混淆工具GUI..."
echo "   (关闭GUI窗口即可退出)"
echo ""

# 设置环境变量，确保子进程使用正确的Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 启动GUI并捕获退出状态
python "$PROJECT_ROOT/oc_gui.py"
exit_code=$?

# 退出虚拟环境
deactivate

if [ $exit_code -eq 0 ]; then
    echo ""
    echo "✅ GUI已正常关闭"
else
    echo ""
    echo "⚠️  GUI退出时出现异常 (退出代码: $exit_code)"
fi

echo "🏁 完成" 