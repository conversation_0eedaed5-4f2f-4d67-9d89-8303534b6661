#!/bin/bash
# test_cli_integration.sh - CLI工具集成测试脚本
# 用于测试打包后的CLI工具的功能

echo "🧪 CLI工具集成测试"
echo "=================================="

# 检查CLI工具是否存在
if [ ! -f "dist/oc_cli" ]; then
    echo "❌ 错误：未找到 dist/oc_cli 文件"
    echo "请先运行 ./build_cli.sh 进行打包"
    exit 1
fi

echo "✅ 找到CLI工具: dist/oc_cli"
echo "📊 文件大小: $(du -h dist/oc_cli | cut -f1)"
echo ""

# 测试1: 帮助信息
echo "🔍 测试1: 帮助信息"
if ./dist/oc_cli --help > /dev/null 2>&1; then
    echo "✅ 帮助信息显示正常"
else
    echo "❌ 帮助信息显示失败"
    exit 1
fi

# 测试2: 版本信息（如果有的话）
echo ""
echo "🔍 测试2: 基本参数解析"
if ./dist/oc_cli --sdk-region 2 --functions 0 --help > /dev/null 2>&1; then
    echo "✅ 参数解析正常"
else
    echo "❌ 参数解析失败"
fi

# 测试3: 模拟后端调用
echo ""
echo "🔍 测试3: 模拟后端调用"

# 创建一个简单的测试脚本
cat > temp_test_backend.sh << 'EOF'
#!/bin/bash
# 模拟PHP后端调用
echo "模拟PHP后端调用CLI工具..."

# 构建命令
CLI_PATH="./dist/oc_cli"
INPUT_PATH="/Users/<USER>/jumbo/xianxian/XXGPlayKit"  # 假设的路径
SDK_REGION="2"
FUNCTIONS="0"

# 执行命令（只显示帮助，避免实际执行混淆）
CMD="$CLI_PATH --help"

echo "执行命令: $CMD"
$CMD > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 后端调用测试成功"
    return 0
else
    echo "❌ 后端调用测试失败"
    return 1
fi
EOF

chmod +x temp_test_backend.sh
if ./temp_test_backend.sh; then
    echo "✅ 后端集成测试通过"
else
    echo "❌ 后端集成测试失败"
fi

# 清理测试文件
rm -f temp_test_backend.sh

echo ""
echo "🔍 测试4: CLI工具依赖检查"
# 检查是否能正常导入所需模块
if ./dist/oc_cli --help 2>&1 | grep -q "usage:"; then
    echo "✅ 所有依赖模块正常"
else
    echo "❌ 依赖模块可能存在问题"
fi

echo ""
echo "🔍 测试5: 文件权限检查"
if [ -x "dist/oc_cli" ]; then
    echo "✅ 文件具有执行权限"
else
    echo "❌ 文件缺少执行权限"
    chmod +x dist/oc_cli
    echo "✅ 已修复执行权限"
fi

echo ""
echo "=================================="
echo "🎉 CLI工具集成测试完成！"
echo ""
echo "📖 使用说明："
echo "   1. CLI工具位置: dist/oc_cli"
echo "   2. 文件大小: $(du -h dist/oc_cli | cut -f1)"
echo "   3. 后端集成示例: backend_examples/"
echo ""
echo "🚀 后端调用示例："
echo "   # PHP"
echo "   exec('./dist/oc_cli --input-path /path/to/project --sdk-region 2');"
echo ""
echo "   # Node.js"
echo "   exec('./dist/oc_cli --input-path /path/to/project --sdk-region 2', callback);"
echo ""
echo "   # Python"
echo "   subprocess.run(['./dist/oc_cli', '--input-path', '/path/to/project', '--sdk-region', '2'])"
echo ""
echo "💡 提示："
echo "   - CLI工具自带Python运行环境，无需安装Python"
echo "   - 可以在没有项目依赖的机器上直接运行"
echo "   - 适合集成到PHP、Node.js、Python等后端系统" 