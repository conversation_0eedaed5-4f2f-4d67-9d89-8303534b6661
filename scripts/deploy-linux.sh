#!/bin/bash
# deploy-linux.sh - Linux服务器自动部署脚本
# 在CentOS/Linux系统上构建OC混淆工具

echo "🐧 Linux服务器部署OC混淆工具"
echo "=========================="

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检测Linux发行版
if [ -f /etc/redhat-release ]; then
    DISTRO="redhat"
    echo "📋 检测到RedHat系Linux (CentOS/RHEL/Rocky Linux/AlmaLinux)"
elif [ -f /etc/debian_version ]; then
    DISTRO="debian"
    echo "📋 检测到Debian系Linux (Ubuntu/Debian)"
else
    DISTRO="unknown"
    echo "⚠️  未识别的Linux发行版，尝试通用方法"
fi

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3，正在安装..."
    
    case $DISTRO in
        "redhat")
            # 检查是否有dnf (CentOS 8+)
            if command -v dnf &> /dev/null; then
                sudo dnf install -y python3 python3-pip python3-venv git
            else
                sudo yum install -y python3 python3-pip python3-venv git
            fi
            ;;
        "debian")
            sudo apt update
            sudo apt install -y python3 python3-pip python3-venv git
            ;;
        *)
            echo "❌ 无法自动安装Python3，请手动安装后重试"
            exit 1
            ;;
    esac
else
    echo "✅ 找到Python3: $(python3 --version)"
fi

# 检查pip3
if ! command -v pip3 &> /dev/null; then
    echo "❌ 未找到pip3，正在安装..."
    
    case $DISTRO in
        "redhat")
            if command -v dnf &> /dev/null; then
                sudo dnf install -y python3-pip
            else
                sudo yum install -y python3-pip
            fi
            ;;
        "debian")
            sudo apt install -y python3-pip
            ;;
    esac
fi

# 创建虚拟环境
echo "🔧 创建虚拟环境..."
if [ -d "venv" ]; then
    echo "⚠️  虚拟环境已存在，将重新创建..."
    rm -rf venv
fi

python3 -m venv venv
if [ $? -ne 0 ]; then
    echo "❌ 创建虚拟环境失败"
    exit 1
fi

# 激活虚拟环境
echo "🎯 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装项目依赖
echo "📚 安装项目依赖..."
if [ -f "requirements.txt" ]; then
    # 尝试正常安装
    pip install -r requirements.txt
    
    if [ $? -ne 0 ]; then
        echo "⚠️  正常安装失败，尝试使用国内镜像源..."
        pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
        
        if [ $? -ne 0 ]; then
            echo "❌ 依赖安装失败"
            deactivate
            exit 1
        fi
    fi
else
    echo "❌ 未找到requirements.txt文件"
    deactivate
    exit 1
fi

# 安装PyInstaller
echo "🔨 安装PyInstaller..."
pip install pyinstaller

# 验证关键模块
echo "🧪 验证关键模块..."
python -c "import yaml, pbxproj, numpy; print('✅ 关键模块验证通过')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 关键模块验证失败"
    deactivate
    exit 1
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf dist/oc_cli* build/oc_cli* oc_cli.spec

# 开始打包
echo "📦 开始打包CLI工具..."
pyinstaller \
    --onefile \
    --name="oc_cli" \
    --console \
    --add-data "ObjectiveC:ObjectiveC" \
    --add-data "配置文件:配置文件" \
    --exclude-module tkinter \
    --exclude-module matplotlib \
    --exclude-module pandas \
    --hidden-import yaml \
    --hidden-import pbxproj \
    --hidden-import numpy \
    --hidden-import markdown \
    --hidden-import bs4 \
    --hidden-import pygments \
    --hidden-import PIL \
    --hidden-import PIL.Image \
    --clean \
    oc_cli.py

# 检查打包结果
if [ -f "dist/oc_cli" ]; then
    echo "✅ CLI工具打包完成！"
    
    # 设置执行权限
    chmod +x dist/oc_cli
    
    # 显示文件信息
    echo "📁 输出位置: dist/oc_cli"
    echo "📊 文件大小: $(du -h dist/oc_cli | cut -f1)"
    echo "🔍 文件类型: $(file dist/oc_cli)"
    
    # 测试可执行文件
    echo "🧪 测试可执行文件..."
    if timeout 10s ./dist/oc_cli --help > /dev/null 2>&1; then
        echo "✅ CLI工具测试通过！"
    else
        echo "⚠️  CLI工具测试超时或失败，但文件已生成"
    fi
    
    echo ""
    echo "🎉 Linux版本打包成功！"
    echo ""
    echo "📖 使用方法:"
    echo "   ./dist/oc_cli --help"
    echo "   ./dist/oc_cli --input-path /path/to/project --sdk-region 2"
    echo ""
    echo "🚀 后端集成示例:"
    echo "   exec('./dist/oc_cli --input-path \$path --sdk-region 2');"
    echo ""
    echo "💡 提示:"
    echo "   - 可执行文件已包含所有依赖，可以在其他Linux系统上直接运行"
    echo "   - 如需在其他机器使用，只需复制 dist/oc_cli 文件"
    
else
    echo "❌ 打包失败！"
    echo "请检查错误信息并重试"
    deactivate
    exit 1
fi

# 退出虚拟环境
deactivate

echo ""
echo "🏁 部署完成！" 